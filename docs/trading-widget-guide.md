# PumpFun Sniper Trading Widget Guide

## Overview

The PumpFun Sniper Trading Widget is a native GUI application built with Rust and egui that provides real-time monitoring and control of the PumpFun trading bot. It offers a professional dark-themed interface for managing environment variables, monitoring token aging pipelines, tracking active trades, and viewing system logs.

## Features

### 🎛️ Environment Variable Management
- **Real-time Configuration**: Modify trading parameters without restarting the bot
- **Validation**: Built-in validation for numeric values and ranges
- **Categories**: Organized by trading logic, performance, and system settings
- **Persistence**: Changes are automatically saved to `.env` file

### 📊 Token Pipeline Visualization
- **Aging Pipeline**: Real-time view of tokens being evaluated for trading
- **Progress Tracking**: Visual progress bars showing token maturation
- **Score Visualization**: Color-coded scoring system with thresholds
- **Interactive Charts**: Pie charts showing score distribution and pipeline health

### 💹 Active Trade Monitoring
- **Real-time P&L**: Live profit/loss tracking with percentage calculations
- **Position Details**: Entry price, current price, position size, and duration
- **Trade Status**: Visual indicators for different trade states
- **Performance Metrics**: Success rates and trade statistics

### 📋 Terminal Log Viewer
- **Live Logs**: Real-time streaming of bot logs with color coding
- **Search & Filter**: Advanced search functionality with regex support
- **Log Levels**: Filter by ERROR, WARN, INFO, DEBUG, and TRACE levels
- **Export**: Save logs to file for analysis

## Installation & Setup

### Prerequisites
- Rust 1.70+ installed
- Valid `.env` file with trading bot configuration
- PumpFun trading bot codebase

### Building the Widget

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd pumpfun-trading-bot

# Build the trading widget
cargo build --release --bin trading-widget

# Run the widget
cargo run --bin trading-widget
```

### Testing Mode

Run integration tests to verify functionality:

```bash
# Run comprehensive tests
cargo run --bin trading-widget -- --test
```

## Usage Guide

### Starting the Application

1. **Launch the Widget**:
   ```bash
   cargo run --bin trading-widget
   ```

2. **Interface Overview**:
   - **Controls Tab**: Environment variable management
   - **Monitoring Tab**: Token pipeline and trade visualization
   - **Terminal Tab**: Live log viewing and analysis

### Environment Variable Management

#### Accessing Controls
1. Click the **Controls** tab in the main interface
2. Variables are organized into logical categories:
   - **Trading Logic**: Core trading parameters
   - **Performance**: RPC and execution settings
   - **System**: Logging and operational settings

#### Modifying Variables
1. **Locate Variable**: Use the search box or browse categories
2. **Edit Value**: Click on the value field and enter new value
3. **Validation**: Invalid values are highlighted in red
4. **Save**: Changes are automatically saved to `.env` file
5. **Apply**: Restart the trading bot to apply changes

#### Key Variables to Monitor
- `PUMP_MIN_SCORE_AGED_CANDIDATES`: Minimum score threshold for trading
- `PUMP_AGING_PIPELINE_MAX_TRACKED_TOKENS`: Maximum tokens in pipeline
- `PUMP_POSITION_SIZE_PERCENTAGE`: Position sizing (percentage of wallet)
- `PUMP_DIP_TRIGGER_THRESHOLD_PERCENTAGE`: Dip detection sensitivity

### Token Pipeline Monitoring

#### Pipeline Overview
1. **Switch to Monitoring Tab**
2. **Pipeline Status**: View current tokens being evaluated
3. **Progress Visualization**: 
   - Red: Early stage tokens
   - Yellow: Maturing tokens  
   - Green: Ready for trading

#### Understanding Metrics
- **Score**: Composite score based on multiple factors
- **Progress**: Bonding curve completion percentage
- **Age**: Time since token creation
- **Threshold**: Minimum score required for trading

#### Interactive Features
- **Hover Details**: Mouse over tokens for detailed information
- **Real-time Updates**: Data refreshes automatically
- **Color Coding**: Visual indicators for token health

### Active Trade Monitoring

#### Trade Dashboard
1. **Current Position**: View active trade details
2. **P&L Tracking**: Real-time profit/loss calculations
3. **Trade Timeline**: Duration and key milestones
4. **Exit Conditions**: Monitor stop-loss and take-profit levels

#### Performance Metrics
- **Success Rate**: Percentage of profitable trades
- **Average Hold Time**: Typical trade duration
- **Risk Metrics**: Maximum drawdown and exposure

### Log Analysis

#### Accessing Logs
1. **Terminal Tab**: Switch to log viewer
2. **Live Stream**: Logs appear in real-time
3. **Search**: Use the search box for specific events
4. **Filter**: Select log levels to focus on specific types

#### Search Features
- **Text Search**: Find specific messages or errors
- **Regex Support**: Advanced pattern matching
- **Level Filtering**: Focus on errors, warnings, or info
- **Time Range**: Filter by timestamp (if needed)

#### Export Functionality
1. **Select Logs**: Choose time range or filter criteria
2. **Export**: Save to file for external analysis
3. **Format**: Plain text with timestamps and levels

## Configuration Reference

### Critical Environment Variables

#### Trading Parameters
```env
# Minimum score for aged candidates
PUMP_MIN_SCORE_AGED_CANDIDATES=0.7

# Maximum tokens tracked in pipeline
PUMP_AGING_PIPELINE_MAX_TRACKED_TOKENS=8

# Position sizing (percentage of wallet balance)
PUMP_POSITION_SIZE_PERCENTAGE=10

# Dip detection threshold
PUMP_DIP_TRIGGER_THRESHOLD_PERCENTAGE=5.0
```

#### Performance Settings
```env
# RPC request timeout
PUMP_RPC_TIMEOUT_SECONDS=30

# Maximum concurrent RPC calls
PUMP_MAX_CONCURRENT_RPC_CALLS=10

# Priority fee settings
PUMP_PRIORITY_FEE_LAMPORTS=50000
```

#### System Configuration
```env
# Logging level
RUST_LOG=info

# Log file path
PUMP_LOG_FILE_PATH=./logs/trading.log

# Enable performance monitoring
PUMP_ENABLE_PERFORMANCE_MONITORING=true
```

## Troubleshooting

### Common Issues

#### Widget Won't Start
- **Check Rust Version**: Ensure Rust 1.70+ is installed
- **Dependencies**: Run `cargo build` to install dependencies
- **Environment File**: Verify `.env` file exists and is readable

#### No Data Displayed
- **Bot Connection**: Ensure trading bot is running
- **Shared State**: Check if bot and widget are using same state file
- **Permissions**: Verify read/write access to log files

#### Performance Issues
- **System Resources**: Monitor CPU and memory usage
- **Log Volume**: Reduce log level if too many messages
- **Update Frequency**: Adjust refresh rates in configuration

### Error Messages

#### "Failed to load environment variables"
- Check `.env` file exists in project root
- Verify file permissions and format
- Ensure no syntax errors in variable definitions

#### "Cannot connect to trading bot"
- Verify bot is running and accessible
- Check shared state file permissions
- Ensure compatible versions of bot and widget

## Best Practices

### Monitoring Workflow
1. **Start Widget**: Launch before starting trading bot
2. **Check Configuration**: Verify all parameters are correct
3. **Monitor Pipeline**: Watch token flow and scoring
4. **Track Trades**: Monitor active positions closely
5. **Review Logs**: Check for errors or warnings regularly

### Performance Optimization
- **Close Unused Tabs**: Focus on relevant information
- **Adjust Refresh Rates**: Balance responsiveness with performance
- **Log Management**: Rotate logs regularly to prevent large files
- **Resource Monitoring**: Keep an eye on system resource usage

### Safety Considerations
- **Test Changes**: Use test mode before live trading
- **Backup Configuration**: Save working `.env` files
- **Monitor Positions**: Never leave trades unattended
- **Emergency Stop**: Know how to quickly halt trading

## Advanced Features

### Custom Themes
The widget supports theme customization through code modifications:
- Edit `AppColors` struct in `src/bin/gui.rs`
- Modify color values for different UI elements
- Rebuild to apply changes

### Integration with External Tools
- **Log Forwarding**: Configure log output for external analysis
- **API Integration**: Extend widget to connect with external APIs
- **Custom Metrics**: Add application-specific monitoring

### Development and Extension
The widget is built with modularity in mind:
- **Plugin Architecture**: Add new tabs and features
- **Data Sources**: Connect to additional data feeds
- **Export Formats**: Support for different output formats

## Support and Resources

### Documentation
- **Code Documentation**: Run `cargo doc --open` for API docs
- **Architecture Guide**: See `docs/architecture.md`
- **Trading Strategy**: Review `docs/trading-strategy.md`

### Community
- **Issues**: Report bugs on GitHub
- **Discussions**: Join community discussions
- **Contributions**: Submit pull requests for improvements

### Updates
- **Version Checking**: Monitor for new releases
- **Migration Guides**: Follow upgrade instructions
- **Changelog**: Review changes between versions

---

*This guide covers the essential features and usage patterns of the PumpFun Sniper Trading Widget. For advanced configuration and development topics, refer to the additional documentation in the `docs/` directory.*
