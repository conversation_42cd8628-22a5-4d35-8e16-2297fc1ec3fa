{"rustc": 15497389221046826682, "features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"default\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "declared_features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"curve25519-dalek\", \"default\", \"dev-context-only-utils\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"program\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "target": 12656250652134757477, "profile": 5347358027863023418, "path": 2726376836129061232, "deps": [[58942224022519477, "solana_program", false, 16828911855130472940], [65234016722529558, "bincode", false, 13214246299112599914], [424821569244661989, "solana_frozen_abi", false, 3427004693324371323], [757899038044743028, "serde_with", false, 16388852251262415725], [1230783206204459120, "urip<PERSON>e", false, 3240664161681992853], [1470679118034951355, "num_enum", false, 8579526410782363184], [3712811570531045576, "byteorder", false, 2818440425237324517], [4097734106057062256, "bs58", false, 10905529450153649927], [4258399515347749257, "pbkdf2", false, 6354231878397334560], [4413975316754495123, "build_script_build", false, 4449500374917277244], [4731167174326621189, "rand0_7", false, 18277100993242179589], [5092398082731730447, "derivation_path", false, 16575906770688116742], [5157631553186200874, "num_traits", false, 13245867816635635925], [5892263340167577622, "solana_logger", false, 16620672824949149588], [5986029879202738730, "log", false, 14372202331545107381], [6203123018298125816, "borsh", false, 5793612998656206161], [6946689283190175495, "wasm_bindgen", false, 15717294916507790655], [7858942147296547339, "rustversion", false, 6910462189244203060], [7896293946984509699, "bitflags", false, 1155823400624989425], [8008191657135824715, "thiserror", false, 647754102121251511], [8079500665534101559, "siphasher", false, 18063108028463561682], [9077477275112005502, "solana_sdk_macro", false, 13288247973646013860], [9209347893430674936, "hmac", false, 3227968042347772437], [9689903380558560274, "serde", false, 12516910166301618055], [9857275760291862238, "sha2", false, 647396043899039964], [9897246384292347999, "chrono", false, 12451684789356062764], [10504454274054532777, "memmap2", false, 13762967648213754977], [10520923840501062997, "generic_array", false, 9672598881852661238], [10697153736615144157, "libsecp256k1", false, 9801421852985704997], [10889494155287625682, "serde_bytes", false, 13457515909258519551], [11017232866922121725, "sha3", false, 9881944012387597957], [11263754829263059703, "num_derive", false, 15556379924513548747], [11903278875415370753, "itertools", false, 15557825444230966020], [13024038960712206194, "qstring", false, 6411691836672966847], [13208667028893622512, "rand", false, 10799981918500864721], [14074610438553418890, "bytemuck", false, 11225396858520692419], [14165672584825129001, "solana_frozen_abi_macro", false, 12364486498514369267], [15367738274754116744, "serde_json", false, 15610749313805602709], [15407337108592562583, "ed25519_dalek_bip32", false, 16034910234220495517], [15877171306855209770, "qualifier_attr", false, 14371092333558513183], [16257276029081467297, "serde_derive", false, 12582596754196380674], [16912878040847476921, "assert_matches", false, 11876409137701759849], [17475753849556516473, "digest", false, 5703081510299168034], [17917672826516349275, "lazy_static", false, 10720984073450295554], [17987314850127689447, "ed25519_dalek", false, 4457432063933195535], [18066890886671768183, "base64", false, 2078352242049108517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-sdk-597c02896ddafdd6/dep-lib-solana_sdk", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}