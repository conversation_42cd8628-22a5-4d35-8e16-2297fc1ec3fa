{"rustc": 15497389221046826682, "features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_06\"]", "declared_features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_05\", \"rwh_06\", \"tokio\"]", "target": 14667884907678119804, "profile": 8276155916380437441, "path": 17144789611310805219, "deps": [[3666421787376679933, "accesskit", false, 14376939792104219550], [4143744114649553716, "rwh_06", false, 1521230371680411288], [5877456377495598509, "winit", false, 17801348701398764728], [9848946087341386794, "accesskit_macos", false, 11773732224433136088]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/accesskit_winit-7e0e44b1d4dbcaa0/dep-lib-accesskit_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}