{"rustc": 15497389221046826682, "features": "[\"CFArray\", \"CFBase\", \"CFBundle\", \"CFCGTypes\", \"CFData\", \"CFDictionary\", \"CFRunLoop\", \"CFString\", \"CFURL\", \"alloc\", \"bitflags\", \"objc2\", \"std\"]", "declared_features": "[\"CFArray\", \"CFAttributedString\", \"CFAvailability\", \"CFBag\", \"CFBase\", \"CFBinaryHeap\", \"CFBitVector\", \"CFBundle\", \"CFByteOrder\", \"CFCGTypes\", \"CFCalendar\", \"CFCharacterSet\", \"CFData\", \"CFDate\", \"CFDateFormatter\", \"CFDictionary\", \"CFError\", \"CFFileDescriptor\", \"CFFileSecurity\", \"CFLocale\", \"CFMachPort\", \"CFMessagePort\", \"CFNotificationCenter\", \"CFNumber\", \"CFNumberFormatter\", \"CFPlugIn\", \"CFPlugInCOM\", \"CFPreferences\", \"CFPropertyList\", \"CFRunLoop\", \"CFSet\", \"CFSocket\", \"CFStream\", \"CFString\", \"CFStringEncodingExt\", \"CFStringTokenizer\", \"CFTimeZone\", \"CFTree\", \"CFURL\", \"CFURLAccess\", \"CFURLEnumerator\", \"CFUUID\", \"CFUserNotification\", \"CFUtilities\", \"CFXMLNode\", \"CFXMLParser\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"dispatch2\", \"libc\", \"objc2\", \"std\", \"unstable-coerce-pointee\"]", "target": 9250166696766853962, "profile": 8196097686603091492, "path": 2773200438948133617, "deps": [[1386409696764982933, "objc2", false, 13617054467596811644], [7896293946984509699, "bitflags", false, 1155823400624989425]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-foundation-2775f62e7f334d8c/dep-lib-objc2_core_foundation", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}