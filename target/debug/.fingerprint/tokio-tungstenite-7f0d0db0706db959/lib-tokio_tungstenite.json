{"rustc": 15497389221046826682, "features": "[\"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"stream\", \"tokio-native-tls\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "target": 2433367608443825, "profile": 5347358027863023418, "path": 223962045526148326, "deps": [[5986029879202738730, "log", false, 14372202331545107381], [8258418851280347661, "tungstenite", false, 5220263844952880506], [9538054652646069845, "tokio", false, 3402822738676175255], [10629569228670356391, "futures_util", false, 5718041981091045398], [12186126227181294540, "tokio_native_tls", false, 15379632674051008925], [16785601910559813697, "native_tls_crate", false, 4681851213697365472]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tokio-tungstenite-7f0d0db0706db959/dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}