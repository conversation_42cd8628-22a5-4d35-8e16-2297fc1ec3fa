{"rustc": 15497389221046826682, "features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_06\"]", "declared_features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_05\", \"rwh_06\", \"tokio\"]", "target": 14667884907678119804, "profile": 5347358027863023418, "path": 17144789611310805219, "deps": [[3666421787376679933, "accesskit", false, 12442062916621337196], [4143744114649553716, "rwh_06", false, 5272834997823970683], [5877456377495598509, "winit", false, 9476848035374136005], [9848946087341386794, "accesskit_macos", false, 4223805699160301654]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/accesskit_winit-a575b80ae0aeaf72/dep-lib-accesskit_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}