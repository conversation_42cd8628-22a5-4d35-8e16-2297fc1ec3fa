{"rustc": 15497389221046826682, "features": "[\"ashpd\", \"async-std\", \"default\", \"pollster\", \"urlencoding\", \"xdg-portal\"]", "declared_features": "[\"ashpd\", \"async-std\", \"common-controls-v6\", \"default\", \"file-handle-inner\", \"glib-sys\", \"gobject-sys\", \"gtk-sys\", \"gtk3\", \"pollster\", \"tokio\", \"urlencoding\", \"xdg-portal\"]", "target": 2038336923818351611, "profile": 8276155916380437441, "path": 12241325377456504177, "deps": [[1072176636087918192, "build_script_build", false, 17171414092385759816], [4143744114649553716, "raw_window_handle", false, 1521230371680411288], [5986029879202738730, "log", false, 540550529245414849], [6662727387107639740, "objc", false, 5894939240988963722], [6839889263267961860, "objc_id", false, 5680022924686261534], [11358553063310754093, "block", false, 16753754428362865852], [13650921406158031571, "objc_foundation", false, 1828227224083492458], [16431863889745914764, "dispatch", false, 5965639072432601046]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rfd-a6576b75f81398a5/dep-lib-rfd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}