{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 775285158232319892, "profile": 5347358027863023418, "path": 4116893179673126486, "deps": [[*****************, "solana_program", false, 16828911855130472940], [7479230097745373958, "spl_type_length_value", false, 12736998418779391138], [9529943735784919782, "arrayref", false, 6526092932885921554], [11699822774991256268, "spl_pod", false, 1584153254868460594], [12414676574469740085, "spl_tlv_account_resolution", false, 837079119914509873], [14074610438553418890, "bytemuck", false, 11225396858520692419], [14673743079976092479, "spl_program_error", false, 11070521676648826575], [18269786033916185670, "spl_discriminator", false, 9075843029044042837]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spl-transfer-hook-interface-172145d3cb64da23/dep-lib-spl_transfer_hook_interface", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}