{"rustc": 15497389221046826682, "features": "[\"default-hasher\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 5347358027863023418, "path": 3210750874747476265, "deps": [[10842263908529601448, "<PERSON><PERSON><PERSON>", false, 17059389534485245847]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-906594764a609cba/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}