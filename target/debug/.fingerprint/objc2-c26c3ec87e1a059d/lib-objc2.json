{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"catch-all\", \"default\", \"disable-encoding-assertions\", \"exception\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"objc2-proc-macros\", \"relax-sign-encoding\", \"relax-void-encoding\", \"std\", \"unstable-apple-new\", \"unstable-arbitrary-self-types\", \"unstable-autoreleasesafe\", \"unstable-coerce-pointee\", \"unstable-compiler-rt\", \"unstable-gnustep-strict-apple-compat\", \"unstable-objfw\", \"unstable-requires-macos\", \"unstable-static-class\", \"unstable-static-class-inlined\", \"unstable-static-sel\", \"unstable-static-sel-inlined\", \"unstable-winobjc\", \"verify\"]", "target": 14277124324407714732, "profile": 8196097686603091492, "path": 8879552790906046799, "deps": [[1386409696764982933, "build_script_build", false, 3628774627398576538], [4595351064294307802, "objc2_encode", false, 2797503325796573498]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-c26c3ec87e1a059d/dep-lib-objc2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}