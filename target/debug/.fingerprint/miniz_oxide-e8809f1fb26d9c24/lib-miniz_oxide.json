{"rustc": 15497389221046826682, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 2908210774301854779, "path": 13376181979986830128, "deps": [[4018467389006652250, "simd_adler32", false, 8561134003717426615], [15407850927583745935, "adler2", false, 359090262192671836]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-e8809f1fb26d9c24/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}