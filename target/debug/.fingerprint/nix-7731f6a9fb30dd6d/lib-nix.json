{"rustc": 15497389221046826682, "features": "[\"fs\", \"process\", \"signal\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"syslog\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 1600181213338542824, "profile": 8276155916380437441, "path": 16609081994762546642, "deps": [[2924422107542798392, "libc", false, 10847358546128459014], [5150833351789356492, "build_script_build", false, 7659399701151197939], [7896293946984509699, "bitflags", false, 1786756380304190567], [10411997081178400487, "cfg_if", false, 10939632757126423159]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-7731f6a9fb30dd6d/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}