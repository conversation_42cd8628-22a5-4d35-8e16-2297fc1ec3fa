{"rustc": 15497389221046826682, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 5347358027863023418, "path": 6765304383427447044, "deps": [[1906322745568073236, "pin_project_lite", false, 10122047953940016221], [3129130049864710036, "memchr", false, 14940508132100173310], [7620660491849607393, "futures_core", false, 7206792896077970838], [9538054652646069845, "tokio", false, 3402822738676175255], [9556762810601084293, "brotli", false, 3225991161561478863], [10563170702865159712, "flate2", false, 8044724582888472567]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-compression-9ae389b069e6b138/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}