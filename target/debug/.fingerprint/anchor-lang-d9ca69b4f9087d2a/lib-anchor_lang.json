{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-syn\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\"]", "target": 8056595410447424620, "profile": 8276155916380437441, "path": 119710664080376573, "deps": [[*****************, "solana_program", false, 918601295916137511], [*****************, "bincode", false, 16353582623063224534], [1887265696381199940, "anchor_attribute_program", false, 7670933943735485876], [2611905835808443941, "borsh", false, 9840601113008650094], [2872329414160291446, "anchor_attribute_account", false, 3627835114503698456], [5111696347431882526, "anchor_derive_space", false, 4694105795028706591], [5153504383575878548, "anchor_attribute_constant", false, 11285289177863033564], [6350578631969742898, "anchor_derive_accounts", false, 18145333354097076398], [7795340888724370787, "anchor_attribute_access_control", false, 3263015257442765395], [8008191657135824715, "thiserror", false, 9973082643837402726], [9529943735784919782, "arrayref", false, 10826664316956858232], [9920160576179037441, "getrandom", false, 13703123510839561981], [11279643347819721421, "anchor_attribute_event", false, 8250175181614636508], [14074610438553418890, "bytemuck", false, 7485110885823545043], [14449956356408373298, "anchor_attribute_error", false, 16788699731684540934], [15379490376034388359, "anchor_derive_serde", false, 2354479883643819044], [17282734725213053079, "base64", false, 6343251386077460873]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-d9ca69b4f9087d2a/dep-lib-anchor_lang", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}