{"$message_type":"diagnostic","message":"this method takes 4 arguments but 3 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":20673,"byte_end":20725,"line_start":537,"line_end":537,"column_start":37,"column_end":89,"is_primary":false,"text":[{"text":"            ui.painter().rect_stroke(rect, 6.0, Stroke::new(1.0, self.colors.secondary));","highlight_start":37,"highlight_end":89}],"label":"argument #4 of type `StrokeKind` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":20662,"byte_end":20673,"line_start":537,"line_end":537,"column_start":26,"column_end":37,"is_primary":true,"text":[{"text":"            ui.painter().rect_stroke(rect, 6.0, Stroke::new(1.0, self.colors.secondary));","highlight_start":26,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/egui-0.31.1/src/painter.rs","byte_start":14168,"byte_end":14179,"line_start":438,"line_end":438,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn rect_stroke(","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/bin/gui.rs","byte_start":20673,"byte_end":20725,"line_start":537,"line_end":537,"column_start":37,"column_end":89,"is_primary":true,"text":[{"text":"            ui.painter().rect_stroke(rect, 6.0, Stroke::new(1.0, self.colors.secondary));","highlight_start":37,"highlight_end":89}],"label":null,"suggested_replacement":"(rect, 6.0, Stroke::new(1.0, self.colors.secondary), /* StrokeKind */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 4 arguments but 3 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:537:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m537\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ui.painter().rect_stroke(rect, 6.0, Stroke::new(1.0, self.colors.secondary));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #4 of type `StrokeKind` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/egui-0.31.1/src/painter.rs:438:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m438\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rect_stroke(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m537\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            ui.painter().rect_stroke(rect, 6.0, Stroke::new(1.0, self.colors.secondary)\u001b[0m\u001b[0m\u001b[38;5;10m, /* StrokeKind */\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                        \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 4 arguments but 3 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":31837,"byte_end":31889,"line_start":801,"line_end":801,"column_start":33,"column_end":85,"is_primary":false,"text":[{"text":"        ui.painter().rect_stroke(rect, 8.0, Stroke::new(1.5, self.colors.secondary));","highlight_start":33,"highlight_end":85}],"label":"argument #4 of type `StrokeKind` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":31826,"byte_end":31837,"line_start":801,"line_end":801,"column_start":22,"column_end":33,"is_primary":true,"text":[{"text":"        ui.painter().rect_stroke(rect, 8.0, Stroke::new(1.5, self.colors.secondary));","highlight_start":22,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/egui-0.31.1/src/painter.rs","byte_start":14168,"byte_end":14179,"line_start":438,"line_end":438,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn rect_stroke(","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/bin/gui.rs","byte_start":31837,"byte_end":31889,"line_start":801,"line_end":801,"column_start":33,"column_end":85,"is_primary":true,"text":[{"text":"        ui.painter().rect_stroke(rect, 8.0, Stroke::new(1.5, self.colors.secondary));","highlight_start":33,"highlight_end":85}],"label":null,"suggested_replacement":"(rect, 8.0, Stroke::new(1.5, self.colors.secondary), /* StrokeKind */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 4 arguments but 3 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:801:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m801\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ui.painter().rect_stroke(rect, 8.0, Stroke::new(1.5, self.colors.secondary));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #4 of type `StrokeKind` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/egui-0.31.1/src/painter.rs:438:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m438\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rect_stroke(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m801\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        ui.painter().rect_stroke(rect, 8.0, Stroke::new(1.5, self.colors.secondary)\u001b[0m\u001b[0m\u001b[38;5;10m, /* StrokeKind */\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                    \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `show_tooltip_at_pointer` found for reference `&egui::Context` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":46213,"byte_end":46222,"line_start":1139,"line_end":1139,"column_start":21,"column_end":30,"is_primary":false,"text":[{"text":"                    ui.ctx().show_tooltip_at_pointer(egui::Id::new(\"pie_tooltip\"), |ui| {","highlight_start":21,"highlight_end":30}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":46222,"byte_end":46245,"line_start":1139,"line_end":1139,"column_start":30,"column_end":53,"is_primary":true,"text":[{"text":"                    ui.ctx().show_tooltip_at_pointer(egui::Id::new(\"pie_tooltip\"), |ui| {","highlight_start":30,"highlight_end":53}],"label":"method not found in `&Context`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `show_tooltip_at_pointer` found for reference `&egui::Context` in the current scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:1139:30\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    ui.ctx().show_tooltip_at_pointer(egui::Id::new(\"pie_tooltip\"), |ui| {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `&Context`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 4 arguments but 3 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":67013,"byte_end":67198,"line_start":1618,"line_end":1622,"column_start":53,"column_end":30,"is_primary":false,"text":[{"text":"                            ui.painter().rect_stroke(","highlight_start":53,"highlight_end":54},{"text":"                                highlight_rect,","highlight_start":1,"highlight_end":48},{"text":"                                2.0,","highlight_start":1,"highlight_end":37},{"text":"                                Stroke::new(1.0, self.colors.accent)","highlight_start":1,"highlight_end":69},{"text":"                            );","highlight_start":1,"highlight_end":30}],"label":"argument #4 of type `StrokeKind` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":67002,"byte_end":67013,"line_start":1618,"line_end":1618,"column_start":42,"column_end":53,"is_primary":true,"text":[{"text":"                            ui.painter().rect_stroke(","highlight_start":42,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/egui-0.31.1/src/painter.rs","byte_start":14168,"byte_end":14179,"line_start":438,"line_end":438,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn rect_stroke(","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src/bin/gui.rs","byte_start":67013,"byte_end":67198,"line_start":1618,"line_end":1622,"column_start":53,"column_end":30,"is_primary":true,"text":[{"text":"                            ui.painter().rect_stroke(","highlight_start":53,"highlight_end":54},{"text":"                                highlight_rect,","highlight_start":1,"highlight_end":48},{"text":"                                2.0,","highlight_start":1,"highlight_end":37},{"text":"                                Stroke::new(1.0, self.colors.accent)","highlight_start":1,"highlight_end":69},{"text":"                            );","highlight_start":1,"highlight_end":30}],"label":null,"suggested_replacement":"(highlight_rect, 2.0, Stroke::new(1.0, self.colors.accent), /* StrokeKind */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 4 arguments but 3 arguments were supplied\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:1618:42\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1618\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   ui.painter().rect_stroke(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ____________________________________\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1619\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                       highlight_rect,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1620\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                       2.0,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1621\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                       Stroke::new(1.0, self.colors.accent)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1622\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m                   );\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_______________________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #4 of type `StrokeKind` is missing\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/egui-0.31.1/src/painter.rs:438:12\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m438\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn rect_stroke(\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1618\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                            ui.painter().rect_stroke\u001b[0m\u001b[0m\u001b[38;5;9m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1619\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-                                 highlight_rect,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1620\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-                                 2.0,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1621\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-                                 Stroke::new(1.0, self.colors.accent)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1622\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-                             )\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1618\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                            ui.painter().rect_stroke\u001b[0m\u001b[0m\u001b[38;5;10m(highlight_rect, 2.0, Stroke::new(1.0, self.colors.accent), /* StrokeKind */)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `gradient_color2`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/gui.rs","byte_start":8204,"byte_end":8219,"line_start":227,"line_end":227,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"                let gradient_color2 = Color32::from_rgb(35, 35, 40);","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/gui.rs","byte_start":8204,"byte_end":8219,"line_start":227,"line_end":227,"column_start":21,"column_end":36,"is_primary":true,"text":[{"text":"                let gradient_color2 = Color32::from_rgb(35, 35, 40);","highlight_start":21,"highlight_end":36}],"label":null,"suggested_replacement":"_gradient_color2","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `gradient_color2`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:227:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let gradient_color2 = Color32::from_rgb(35, 35, 40);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_gradient_color2`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":12580,"byte_end":12584,"line_start":328,"line_end":328,"column_start":76,"column_end":80,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {","highlight_start":76,"highlight_end":80}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12525,"byte_end":12673,"line_start":328,"line_end":330,"column_start":21,"column_end":23,"is_primary":true,"text":[{"text":"                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {","highlight_start":21,"highlight_end":82},{"text":"                        self.render_token_pipeline_section(ui);","highlight_start":1,"highlight_end":64},{"text":"                    });","highlight_start":1,"highlight_end":23}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12611,"byte_end":12615,"line_start":329,"line_end":329,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"                        self.render_token_pipeline_section(ui);","highlight_start":25,"highlight_end":29}],"label":"first borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12530,"byte_end":12551,"line_start":328,"line_end":328,"column_start":26,"column_end":47,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {","highlight_start":26,"highlight_end":47}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:328:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _____________________|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m329\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        self.render_token_pipeline_section(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow occurs due to use of `*self` in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m330\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|______________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":12525,"byte_end":12529,"line_start":328,"line_end":328,"column_start":21,"column_end":25,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {","highlight_start":21,"highlight_end":25}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12580,"byte_end":12584,"line_start":328,"line_end":328,"column_start":76,"column_end":80,"is_primary":true,"text":[{"text":"                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {","highlight_start":76,"highlight_end":80}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12611,"byte_end":12615,"line_start":329,"line_end":329,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"                        self.render_token_pipeline_section(ui);","highlight_start":25,"highlight_end":29}],"label":"second borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12530,"byte_end":12551,"line_start":328,"line_end":328,"column_start":26,"column_end":47,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {","highlight_start":26,"highlight_end":47}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:328:76\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.render_styled_section(ui, \"Token Aging Pipeline\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m329\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        self.render_token_pipeline_section(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12msecond borrow occurs due to use of `*self` in closure\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":12861,"byte_end":12865,"line_start":335,"line_end":335,"column_start":76,"column_end":80,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {","highlight_start":76,"highlight_end":80}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12806,"byte_end":12952,"line_start":335,"line_end":337,"column_start":21,"column_end":23,"is_primary":true,"text":[{"text":"                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {","highlight_start":21,"highlight_end":82},{"text":"                        self.render_active_trade_section(ui);","highlight_start":1,"highlight_end":62},{"text":"                    });","highlight_start":1,"highlight_end":23}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12892,"byte_end":12896,"line_start":336,"line_end":336,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"                        self.render_active_trade_section(ui);","highlight_start":25,"highlight_end":29}],"label":"first borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12811,"byte_end":12832,"line_start":335,"line_end":335,"column_start":26,"column_end":47,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {","highlight_start":26,"highlight_end":47}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:335:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _____________________|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        self.render_active_trade_section(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow occurs due to use of `*self` in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|______________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":12806,"byte_end":12810,"line_start":335,"line_end":335,"column_start":21,"column_end":25,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {","highlight_start":21,"highlight_end":25}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12861,"byte_end":12865,"line_start":335,"line_end":335,"column_start":76,"column_end":80,"is_primary":true,"text":[{"text":"                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {","highlight_start":76,"highlight_end":80}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12892,"byte_end":12896,"line_start":336,"line_end":336,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"                        self.render_active_trade_section(ui);","highlight_start":25,"highlight_end":29}],"label":"second borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":12811,"byte_end":12832,"line_start":335,"line_end":335,"column_start":26,"column_end":47,"is_primary":false,"text":[{"text":"                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {","highlight_start":26,"highlight_end":47}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:335:76\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    self.render_styled_section(ui, \"Active Trade Monitor\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        self.render_active_trade_section(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12msecond borrow occurs due to use of `*self` in closure\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":13196,"byte_end":13200,"line_start":343,"line_end":343,"column_start":82,"column_end":86,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {","highlight_start":82,"highlight_end":86}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13139,"byte_end":13292,"line_start":343,"line_end":345,"column_start":25,"column_end":27,"is_primary":true,"text":[{"text":"                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {","highlight_start":25,"highlight_end":88},{"text":"                            self.render_advanced_controls(ui);","highlight_start":1,"highlight_end":63},{"text":"                        });","highlight_start":1,"highlight_end":27}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13231,"byte_end":13235,"line_start":344,"line_end":344,"column_start":29,"column_end":33,"is_primary":false,"text":[{"text":"                            self.render_advanced_controls(ui);","highlight_start":29,"highlight_end":33}],"label":"first borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13144,"byte_end":13165,"line_start":343,"line_end":343,"column_start":30,"column_end":51,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {","highlight_start":30,"highlight_end":51}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:343:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _________________________|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m344\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            self.render_advanced_controls(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow occurs due to use of `*self` in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m345\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|__________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":13139,"byte_end":13143,"line_start":343,"line_end":343,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {","highlight_start":25,"highlight_end":29}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13196,"byte_end":13200,"line_start":343,"line_end":343,"column_start":82,"column_end":86,"is_primary":true,"text":[{"text":"                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {","highlight_start":82,"highlight_end":86}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13231,"byte_end":13235,"line_start":344,"line_end":344,"column_start":29,"column_end":33,"is_primary":false,"text":[{"text":"                            self.render_advanced_controls(ui);","highlight_start":29,"highlight_end":33}],"label":"second borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13144,"byte_end":13165,"line_start":343,"line_end":343,"column_start":30,"column_end":51,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {","highlight_start":30,"highlight_end":51}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:343:82\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        self.render_styled_section(ui, \"Advanced Configuration\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m344\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            self.render_advanced_controls(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12msecond borrow occurs due to use of `*self` in closure\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":13403,"byte_end":13407,"line_start":347,"line_end":347,"column_start":81,"column_end":85,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {","highlight_start":81,"highlight_end":85}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13347,"byte_end":13496,"line_start":347,"line_end":349,"column_start":25,"column_end":27,"is_primary":true,"text":[{"text":"                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {","highlight_start":25,"highlight_end":87},{"text":"                            self.render_basic_controls(ui);","highlight_start":1,"highlight_end":60},{"text":"                        });","highlight_start":1,"highlight_end":27}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13438,"byte_end":13442,"line_start":348,"line_end":348,"column_start":29,"column_end":33,"is_primary":false,"text":[{"text":"                            self.render_basic_controls(ui);","highlight_start":29,"highlight_end":33}],"label":"first borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13352,"byte_end":13373,"line_start":347,"line_end":347,"column_start":30,"column_end":51,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {","highlight_start":30,"highlight_end":51}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:347:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _________________________|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            self.render_basic_controls(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow occurs due to use of `*self` in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m349\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|__________________________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `*self` as mutable more than once at a time","code":{"code":"E0499","explanation":"A variable was borrowed as mutable more than once.\n\nErroneous code example:\n\n```compile_fail,E0499\nlet mut i = 0;\nlet mut x = &mut i;\nlet mut a = &mut i;\nx;\n// error: cannot borrow `i` as mutable more than once at a time\n```\n\nPlease note that in Rust, you can either have many immutable references, or one\nmutable reference. For more details you may want to read the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n\nExample:\n\n```\nlet mut i = 0;\nlet mut x = &mut i; // ok!\n\n// or:\nlet mut i = 0;\nlet a = &i; // ok!\nlet b = &i; // still ok!\nlet c = &i; // still ok!\nb;\na;\n```\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":13347,"byte_end":13351,"line_start":347,"line_end":347,"column_start":25,"column_end":29,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {","highlight_start":25,"highlight_end":29}],"label":"first mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13403,"byte_end":13407,"line_start":347,"line_end":347,"column_start":81,"column_end":85,"is_primary":true,"text":[{"text":"                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {","highlight_start":81,"highlight_end":85}],"label":"second mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13438,"byte_end":13442,"line_start":348,"line_end":348,"column_start":29,"column_end":33,"is_primary":false,"text":[{"text":"                            self.render_basic_controls(ui);","highlight_start":29,"highlight_end":33}],"label":"second borrow occurs due to use of `*self` in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":13352,"byte_end":13373,"line_start":347,"line_end":347,"column_start":30,"column_end":51,"is_primary":false,"text":[{"text":"                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {","highlight_start":30,"highlight_end":51}],"label":"first borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0499]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `*self` as mutable more than once at a time\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:347:81\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        self.render_styled_section(ui, \"Trading Configuration\", |ui| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9msecond mutable borrow occurs here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst borrow later used by call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfirst mutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                            self.render_basic_controls(ui);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12msecond borrow occurs due to use of `*self` in closure\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot borrow `self.env_manager` as mutable because it is also borrowed as immutable","code":{"code":"E0502","explanation":"A variable already borrowed with a certain mutability (either mutable or\nimmutable) was borrowed again with a different mutability.\n\nErroneous code example:\n\n```compile_fail,E0502\nfn bar(x: &mut i32) {}\nfn foo(a: &mut i32) {\n    let y = &a; // a is borrowed as immutable.\n    bar(a); // error: cannot borrow `*a` as mutable because `a` is also borrowed\n            //        as immutable\n    println!(\"{}\", y);\n}\n```\n\nTo fix this error, ensure that you don't have any other references to the\nvariable before trying to access it with a different mutability:\n\n```\nfn bar(x: &mut i32) {}\nfn foo(a: &mut i32) {\n    bar(a);\n    let y = &a; // ok!\n    println!(\"{}\", y);\n}\n```\n\nFor more information on Rust's ownership system, take a look at the\n[References & Borrowing][references-and-borrowing] section of the Book.\n\n[references-and-borrowing]: https://doc.rust-lang.org/book/ch04-02-references-and-borrowing.html\n"},"level":"error","spans":[{"file_name":"src/bin/gui.rs","byte_start":58763,"byte_end":58816,"line_start":1435,"line_end":1435,"column_start":21,"column_end":74,"is_primary":true,"text":[{"text":"            let _ = self.env_manager.update_variable(&key, default_value);","highlight_start":21,"highlight_end":74}],"label":"mutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":58474,"byte_end":58500,"line_start":1426,"line_end":1426,"column_start":40,"column_end":66,"is_primary":false,"text":[{"text":"                    if let Some(var) = self.env_manager.variables.get(&key) {","highlight_start":40,"highlight_end":66}],"label":"immutable borrow occurs here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/gui.rs","byte_start":58780,"byte_end":58795,"line_start":1435,"line_end":1435,"column_start":38,"column_end":53,"is_primary":false,"text":[{"text":"            let _ = self.env_manager.update_variable(&key, default_value);","highlight_start":38,"highlight_end":53}],"label":"immutable borrow later used by call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0502]\u001b[0m\u001b[0m\u001b[1m: cannot borrow `self.env_manager` as mutable because it is also borrowed as immutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/gui.rs:1435:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1426\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    if let Some(var) = self.env_manager.variables.get(&key) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mimmutable borrow occurs here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1435\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let _ = self.env_manager.update_variable(&key, default_value);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mimmutable borrow later used by call\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmutable borrow occurs here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 13 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 13 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0499, E0502, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0061, E0499, E0502, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
