{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-syn\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\"]", "target": 8056595410447424620, "profile": 5347358027863023418, "path": 119710664080376573, "deps": [[*****************, "solana_program", false, 16828911855130472940], [*****************, "bincode", false, 13214246299112599914], [1887265696381199940, "anchor_attribute_program", false, 6648853932691312909], [2611905835808443941, "borsh", false, 1071404397845648424], [2872329414160291446, "anchor_attribute_account", false, 12347321880749899161], [5111696347431882526, "anchor_derive_space", false, 4694105795028706591], [5153504383575878548, "anchor_attribute_constant", false, 17448802645688526318], [6350578631969742898, "anchor_derive_accounts", false, 7036784529947930410], [7795340888724370787, "anchor_attribute_access_control", false, 10927618749409579467], [8008191657135824715, "thiserror", false, 647754102121251511], [9529943735784919782, "arrayref", false, 6526092932885921554], [9920160576179037441, "getrandom", false, 3525052169394236698], [11279643347819721421, "anchor_attribute_event", false, 14886568563606369228], [14074610438553418890, "bytemuck", false, 11225396858520692419], [14449956356408373298, "anchor_attribute_error", false, 10818464869021172069], [15379490376034388359, "anchor_derive_serde", false, 14208150658623761030], [17282734725213053079, "base64", false, 17992513006356458742]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/anchor-lang-9c8fdf41f7718c79/dep-lib-anchor_lang", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}