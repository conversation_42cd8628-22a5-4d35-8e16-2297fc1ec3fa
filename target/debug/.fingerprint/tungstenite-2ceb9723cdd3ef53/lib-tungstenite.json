{"rustc": 15497389221046826682, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 5347358027863023418, "path": 3505162963686476124, "deps": [[99287295355353247, "data_encoding", false, 1103576837535515956], [3150220818285335163, "url", false, 17873424883298442885], [3712811570531045576, "byteorder", false, 2818440425237324517], [4359956005902820838, "utf8", false, 7632767470029744168], [5986029879202738730, "log", false, 14372202331545107381], [6163892036024256188, "httparse", false, 4998681773345092343], [8008191657135824715, "thiserror", false, 647754102121251511], [9010263965687315507, "http", false, 15190774565033300876], [10724389056617919257, "sha1", false, 9687983321298792917], [13208667028893622512, "rand", false, 10799981918500864721], [16066129441945555748, "bytes", false, 16337691178649358567], [16785601910559813697, "native_tls_crate", false, 4681851213697365472]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-2ceb9723cdd3ef53/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}