{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"apple\", \"catch-all\", \"default\", \"exception\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"malloc\", \"malloc_buf\", \"objc2-proc-macros\", \"relax-sign-encoding\", \"relax-void-encoding\", \"std\", \"unstable-apple-new\", \"unstable-autoreleasesafe\", \"unstable-c-unwind\", \"unstable-compiler-rt\", \"unstable-msg-send-always-comma\", \"unstable-static-class\", \"unstable-static-class-inlined\", \"unstable-static-sel\", \"unstable-static-sel-inlined\", \"verify\"]", "target": 14277124324407714732, "profile": 6305132673592726323, "path": 17791182220372082426, "deps": [[4595351064294307802, "objc2_encode", false, 2797503325796573498], [6452682797676277333, "objc_sys", false, 18147991481929392932]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-992f60d151d7388e/dep-lib-objc2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}