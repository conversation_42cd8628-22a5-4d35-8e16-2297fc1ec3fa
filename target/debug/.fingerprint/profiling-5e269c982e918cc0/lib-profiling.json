{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 5347358027863023418, "path": 8428260922478351863, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/profiling-5e269c982e918cc0/dep-lib-profiling", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}