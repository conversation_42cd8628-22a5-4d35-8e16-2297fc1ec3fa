{"rustc": 15497389221046826682, "features": "[\"ashpd\", \"async-std\", \"default\", \"pollster\", \"urlencoding\", \"xdg-portal\"]", "declared_features": "[\"ashpd\", \"async-std\", \"common-controls-v6\", \"default\", \"file-handle-inner\", \"glib-sys\", \"gobject-sys\", \"gtk-sys\", \"gtk3\", \"pollster\", \"tokio\", \"urlencoding\", \"xdg-portal\"]", "target": 2038336923818351611, "profile": 5347358027863023418, "path": 12241325377456504177, "deps": [[1072176636087918192, "build_script_build", false, 17171414092385759816], [4143744114649553716, "raw_window_handle", false, 5272834997823970683], [5986029879202738730, "log", false, 14372202331545107381], [6662727387107639740, "objc", false, 4126886869503207324], [6839889263267961860, "objc_id", false, 14796566820094640020], [11358553063310754093, "block", false, 8261499189184642790], [13650921406158031571, "objc_foundation", false, 1187699641104188494], [16431863889745914764, "dispatch", false, 6417854589785756966]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rfd-7d10842d51ae2db1/dep-lib-rfd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}