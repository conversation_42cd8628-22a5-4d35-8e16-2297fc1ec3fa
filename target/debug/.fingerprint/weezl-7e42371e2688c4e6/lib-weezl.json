{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 5347358027863023418, "path": 1126800668668022748, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/weezl-7e42371e2688c4e6/dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}