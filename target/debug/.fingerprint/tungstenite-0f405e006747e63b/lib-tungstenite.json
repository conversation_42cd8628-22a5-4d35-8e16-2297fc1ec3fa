{"rustc": 15497389221046826682, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 8276155916380437441, "path": 3505162963686476124, "deps": [[99287295355353247, "data_encoding", false, 7250033480982039313], [3150220818285335163, "url", false, 14465932884787757638], [3712811570531045576, "byteorder", false, 3455893842534120532], [4359956005902820838, "utf8", false, 4742283046145461315], [5986029879202738730, "log", false, 540550529245414849], [6163892036024256188, "httparse", false, 17332265902900947438], [8008191657135824715, "thiserror", false, 9973082643837402726], [9010263965687315507, "http", false, 16323811152373533], [10724389056617919257, "sha1", false, 13301017034223862190], [13208667028893622512, "rand", false, 6475633719892245940], [16066129441945555748, "bytes", false, 1974571504047377931], [16785601910559813697, "native_tls_crate", false, 2390128345332787569]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tungstenite-0f405e006747e63b/dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}