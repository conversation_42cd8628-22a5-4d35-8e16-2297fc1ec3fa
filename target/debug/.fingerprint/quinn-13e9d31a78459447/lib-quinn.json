{"rustc": 15497389221046826682, "features": "[\"default\", \"log\", \"native-certs\", \"ring\", \"runtime-tokio\", \"rustls\", \"tls-rustls\"]", "declared_features": "[\"async-io\", \"async-std\", \"default\", \"futures-io\", \"lock_tracking\", \"log\", \"native-certs\", \"ring\", \"runtime-async-std\", \"runtime-tokio\", \"rustls\", \"tls-rustls\"]", "target": 15407875845472914810, "profile": 5347358027863023418, "path": 5503033928563799346, "deps": [[992440926759950020, "udp", false, 2531396095046681056], [1906322745568073236, "pin_project_lite", false, 10122047953940016221], [8008191657135824715, "thiserror", false, 647754102121251511], [8606274917505247608, "tracing", false, 16533887323355218068], [9538054652646069845, "tokio", false, 3402822738676175255], [11295624341523567602, "rustls", false, 15971944155683504410], [16055916053474393816, "rustc_hash", false, 7447400577716921883], [16066129441945555748, "bytes", false, 16337691178649358567], [18009697307577877652, "proto", false, 13464374258712946179]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/quinn-13e9d31a78459447/dep-lib-quinn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}