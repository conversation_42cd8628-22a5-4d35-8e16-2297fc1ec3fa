{"rustc": 15497389221046826682, "features": "[\"fs\", \"process\", \"signal\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"syslog\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 1600181213338542824, "profile": 5347358027863023418, "path": 16609081994762546642, "deps": [[2924422107542798392, "libc", false, 12451087873132700135], [5150833351789356492, "build_script_build", false, 7659399701151197939], [7896293946984509699, "bitflags", false, 1155823400624989425], [10411997081178400487, "cfg_if", false, 15376794174361114903]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-3985bf76ec8ec054/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}