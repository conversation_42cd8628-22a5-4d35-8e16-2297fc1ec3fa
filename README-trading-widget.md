# PumpFun Sniper Trading Widget

A professional native GUI application for monitoring and controlling the PumpFun trading bot, built with Rust and egui.

## 🚀 Quick Start

### Prerequisites
- Rust 1.70+
- Valid `.env` configuration file
- PumpFun trading bot setup

### Installation
```bash
# Build the widget
cargo build --release --bin trading-widget

# Run the application
cargo run --bin trading-widget

# Run tests
cargo run --bin trading-widget -- --test
```

## ✨ Features

### 🎛️ Environment Management
- **Real-time Configuration**: Modify trading parameters without restarts
- **Validation**: Built-in validation for all parameters
- **Categories**: Organized settings for easy navigation
- **Auto-save**: Changes persist automatically

### 📊 Live Monitoring
- **Token Pipeline**: Real-time visualization of aging tokens
- **Progress Tracking**: Visual progress bars and scoring
- **Interactive Charts**: Pie charts and distribution analysis
- **Color-coded Status**: Instant visual feedback

### 💹 Trade Tracking
- **Active Positions**: Live P&L and position details
- **Performance Metrics**: Success rates and statistics
- **Trade Timeline**: Duration and milestone tracking
- **Risk Monitoring**: Stop-loss and take-profit levels

### 📋 Log Analysis
- **Live Streaming**: Real-time log display with color coding
- **Advanced Search**: Regex support and filtering
- **Level Filtering**: Focus on specific log types
- **Export**: Save logs for external analysis

## 🎨 Interface Overview

### Main Tabs
1. **Controls**: Environment variable management
2. **Monitoring**: Token pipeline and trade visualization  
3. **Terminal**: Live log viewing and analysis

### Key Components
- **Dark Theme**: Professional appearance optimized for trading
- **Responsive Layout**: Adapts to different window sizes
- **Real-time Updates**: Live data refresh without manual intervention
- **Interactive Elements**: Hover details and clickable components

## 🔧 Configuration

### Essential Variables
```env
# Trading Logic
PUMP_MIN_SCORE_AGED_CANDIDATES=0.7
PUMP_AGING_PIPELINE_MAX_TRACKED_TOKENS=8
PUMP_POSITION_SIZE_PERCENTAGE=10

# Performance
PUMP_RPC_TIMEOUT_SECONDS=30
PUMP_MAX_CONCURRENT_RPC_CALLS=10
PUMP_PRIORITY_FEE_LAMPORTS=50000

# System
RUST_LOG=info
PUMP_LOG_FILE_PATH=./logs/trading.log
```

## 📖 Usage

### Starting Up
1. Launch the widget: `cargo run --bin trading-widget`
2. Verify environment variables in Controls tab
3. Monitor token pipeline in Monitoring tab
4. Check logs in Terminal tab for any issues

### Monitoring Workflow
1. **Check Configuration**: Ensure all parameters are correct
2. **Watch Pipeline**: Monitor token scoring and progress
3. **Track Trades**: Keep eye on active positions
4. **Review Logs**: Check for errors or warnings

### Making Changes
1. Navigate to Controls tab
2. Find the variable you want to modify
3. Click on the value field and enter new value
4. Changes are automatically validated and saved
5. Restart trading bot to apply changes

## 🛠️ Development

### Architecture
- **Modular Design**: Separate modules for different functionality
- **Shared State**: Thread-safe communication between components
- **Event-driven**: Reactive updates based on data changes
- **Extensible**: Easy to add new features and tabs

### Key Files
```
src/bin/
├── trading_widget_main.rs  # Main entry point
├── gui.rs                  # Core GUI implementation
├── shared_state.rs         # State management
├── env_manager.rs          # Environment variable handling
├── log_capture.rs          # Log processing
└── integration_test.rs     # Testing framework
```

### Testing
```bash
# Run all tests
cargo run --bin trading-widget -- --test

# Build only (check compilation)
cargo check --bin trading-widget

# Build optimized release
cargo build --release --bin trading-widget
```

## 🔍 Troubleshooting

### Common Issues

**Widget won't start**
- Check Rust version: `rustc --version`
- Verify dependencies: `cargo build`
- Ensure `.env` file exists

**No data displayed**
- Verify trading bot is running
- Check file permissions
- Review log output for errors

**Performance issues**
- Monitor system resources
- Reduce log verbosity
- Close unused applications

### Error Messages

**"Failed to load environment variables"**
- Check `.env` file format and permissions
- Verify all required variables are present

**"Cannot connect to trading bot"**
- Ensure bot is running and accessible
- Check shared state file permissions

## 📚 Documentation

- **Full Guide**: See `docs/trading-widget-guide.md`
- **API Documentation**: Run `cargo doc --open`
- **Architecture**: Review `docs/architecture.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is part of the PumpFun trading bot suite. See the main project license for details.

## 🆘 Support

- **Issues**: Report bugs on GitHub
- **Discussions**: Join community discussions
- **Documentation**: Check the `docs/` directory

---

**⚠️ Trading Disclaimer**: This software is for educational and research purposes. Trading cryptocurrencies involves substantial risk. Always test thoroughly before live trading and never risk more than you can afford to lose.
