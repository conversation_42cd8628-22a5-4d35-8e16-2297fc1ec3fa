#!/bin/bash

# PumpFun Trading Widget Build Script
# This script builds and optionally runs the trading widget

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Rust installation
    if ! command -v rustc &> /dev/null; then
        print_error "Rust is not installed. Please install Rust from https://rustup.rs/"
        exit 1
    fi
    
    # Check Rust version
    RUST_VERSION=$(rustc --version | cut -d' ' -f2)
    print_status "Found Rust version: $RUST_VERSION"
    
    # Check Cargo
    if ! command -v cargo &> /dev/null; then
        print_error "Cargo is not available. Please ensure Rust is properly installed."
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "Cargo.toml" ]; then
        print_error "Cargo.toml not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Check for .env file
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. The widget may not function properly without configuration."
    fi
    
    print_success "Prerequisites check completed"
}

# Function to clean previous builds
clean_build() {
    print_status "Cleaning previous builds..."
    cargo clean
    print_success "Clean completed"
}

# Function to run tests
run_tests() {
    print_status "Running integration tests..."
    
    # Build first to catch compilation errors
    if cargo build --bin trading-widget; then
        print_success "Build successful"
    else
        print_error "Build failed. Please fix compilation errors."
        exit 1
    fi
    
    # Run integration tests
    if cargo run --bin trading-widget -- --test; then
        print_success "All tests passed"
    else
        print_error "Tests failed. Please check the output above."
        exit 1
    fi
}

# Function to build the widget
build_widget() {
    local build_type=$1
    
    print_status "Building trading widget ($build_type mode)..."
    
    if [ "$build_type" = "release" ]; then
        cargo build --release --bin trading-widget
        BINARY_PATH="target/release/trading-widget"
    else
        cargo build --bin trading-widget
        BINARY_PATH="target/debug/trading-widget"
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Build completed successfully"
        print_status "Binary location: $BINARY_PATH"
        
        # Check binary size
        if [ -f "$BINARY_PATH" ]; then
            BINARY_SIZE=$(du -h "$BINARY_PATH" | cut -f1)
            print_status "Binary size: $BINARY_SIZE"
        fi
    else
        print_error "Build failed"
        exit 1
    fi
}

# Function to run the widget
run_widget() {
    print_status "Starting trading widget..."
    print_status "Press Ctrl+C to stop the application"
    echo ""
    
    cargo run --bin trading-widget
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help      Show this help message"
    echo "  -c, --clean     Clean previous builds before building"
    echo "  -t, --test      Run integration tests"
    echo "  -r, --release   Build in release mode (optimized)"
    echo "  -d, --debug     Build in debug mode (default)"
    echo "  --run           Run the widget after building"
    echo "  --test-only     Only run tests, don't build"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build in debug mode"
    echo "  $0 --release --run    # Build in release mode and run"
    echo "  $0 --test --run       # Run tests, build, and run widget"
    echo "  $0 --clean --release  # Clean, then build in release mode"
}

# Main script logic
main() {
    local clean=false
    local test=false
    local release=false
    local run_after=false
    local test_only=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -c|--clean)
                clean=true
                shift
                ;;
            -t|--test)
                test=true
                shift
                ;;
            -r|--release)
                release=true
                shift
                ;;
            -d|--debug)
                release=false
                shift
                ;;
            --run)
                run_after=true
                shift
                ;;
            --test-only)
                test_only=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Print header
    echo "=================================================="
    echo "  PumpFun Trading Widget Build Script"
    echo "=================================================="
    echo ""
    
    # Check prerequisites
    check_prerequisites
    echo ""
    
    # Clean if requested
    if [ "$clean" = true ]; then
        clean_build
        echo ""
    fi
    
    # Run tests if requested
    if [ "$test" = true ] || [ "$test_only" = true ]; then
        run_tests
        echo ""
    fi
    
    # Build unless test-only mode
    if [ "$test_only" = false ]; then
        if [ "$release" = true ]; then
            build_widget "release"
        else
            build_widget "debug"
        fi
        echo ""
    fi
    
    # Run if requested
    if [ "$run_after" = true ] && [ "$test_only" = false ]; then
        run_widget
    fi
    
    # Final message
    if [ "$test_only" = false ]; then
        print_success "Build process completed successfully!"
        echo ""
        print_status "To run the widget manually:"
        echo "  cargo run --bin trading-widget"
        echo ""
        print_status "To run tests:"
        echo "  cargo run --bin trading-widget -- --test"
    else
        print_success "Test process completed successfully!"
    fi
}

# Run main function with all arguments
main "$@"
