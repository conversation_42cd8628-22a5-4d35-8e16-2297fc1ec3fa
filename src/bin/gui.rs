//! Main GUI Implementation
//! 
//! This module contains the main GUI application using egui with dark theming,
//! environment variable controls, and real-time monitoring capabilities.

use eframe::egui;
use egui::{Color32, RichText, Stroke};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

use super::shared_state::{SharedState, TokenInfo, ActiveTrade, TradeStatus, BotMessage};
use super::env_manager::{EnvManager, EnvVarGroup, EnvVarType};
use super::log_capture::{LogCapture, LogCaptureConfig, LogLevel};

/// Main GUI application
pub struct TradingWidgetApp {
    shared_state: Arc<Mutex<SharedState>>,
    env_manager: EnvManager,
    log_capture: LogCapture,
    
    // UI State
    show_advanced: bool,
    show_terminal: bool,
    selected_tab: MainTab,
    
    // Environment variable editing
    env_edit_values: HashMap<String, String>,
    env_validation_errors: HashMap<String, String>,
    
    // Terminal state
    terminal_scroll_to_bottom: bool,
    terminal_search_query: String,
    terminal_auto_scroll: bool,

    // Trading state cache
    trading_active: bool,
    total_trades: u32,

    // Colors and styling
    colors: AppColors,
}

#[derive(Debug, Clone, PartialEq)]
enum MainTab {
    Controls,
    Monitoring,
    Terminal,
}

pub struct AppColors {
    background: Color32,
    surface: Color32,
    primary: Color32,
    secondary: Color32,
    accent: Color32,
    text_primary: Color32,
    text_secondary: Color32,
    success: Color32,
    warning: Color32,
    error: Color32,
    progress_start: Color32,
    progress_middle: Color32,
    progress_end: Color32,
}

impl Default for AppColors {
    fn default() -> Self {
        Self {
            background: Color32::from_rgb(18, 18, 18),      // Very dark background
            surface: Color32::from_rgb(28, 28, 28),         // Dark surface
            primary: Color32::from_rgb(64, 120, 192),       // Blue primary
            secondary: Color32::from_rgb(96, 96, 96),       // Gray secondary
            accent: Color32::from_rgb(255, 193, 7),         // Amber accent
            text_primary: Color32::from_rgb(255, 255, 255), // White text
            text_secondary: Color32::from_rgb(180, 180, 180), // Light gray text
            success: Color32::from_rgb(76, 175, 80),        // Green
            warning: Color32::from_rgb(255, 152, 0),        // Orange
            error: Color32::from_rgb(244, 67, 54),          // Red
            progress_start: Color32::from_rgb(244, 67, 54), // Red start
            progress_middle: Color32::from_rgb(255, 193, 7), // Yellow middle
            progress_end: Color32::from_rgb(76, 175, 80),   // Green end
        }
    }
}

impl AppColors {
    pub fn new() -> Self {
        Self::default()
    }
}

impl TradingWidgetApp {
    pub fn new(shared_state: Arc<Mutex<SharedState>>) -> Self {
        // Initialize environment manager
        let env_manager = EnvManager::new(".env").unwrap_or_else(|e| {
            eprintln!("Failed to load environment manager: {}", e);
            // Create a minimal env manager for testing
            EnvManager::new("test.env").unwrap()
        });
        
        // Initialize log capture with shared state integration
        let log_config = LogCaptureConfig {
            max_entries: 1000,
            capture_enabled: true,
            auto_scroll: true,
            filter_level: LogLevel::Info,
        };

        // Create a channel for log messages to update shared state
        let (log_sender, log_receiver) = std::sync::mpsc::channel();
        let log_capture = LogCapture::new(log_config, Some(log_sender));
        let _ = log_capture.start_capture();

        // Start a thread to process log messages and update shared state
        let shared_state_clone = Arc::clone(&shared_state);
        std::thread::spawn(move || {
            while let Ok(message) = log_receiver.recv() {
                if let BotMessage::LogEntry(entry) = message {
                    // Update shared state based on log content
                    {
                        let mut state = shared_state_clone.lock().unwrap();
                        state.parse_log_message(&entry.message);
                        state.add_log_entry(entry);
                    }
                }
            }
        });
        
        // Initialize environment variable edit values
        let mut env_edit_values = HashMap::new();
        for (key, var) in &env_manager.variables {
            env_edit_values.insert(key.clone(), var.value.clone());
        }
        
        Self {
            shared_state,
            env_manager,
            log_capture,
            show_advanced: false,
            show_terminal: false,
            selected_tab: MainTab::Controls,
            env_edit_values,
            env_validation_errors: HashMap::new(),
            terminal_scroll_to_bottom: true,
            terminal_search_query: String::new(),
            terminal_auto_scroll: true,
            trading_active: false,
            total_trades: 0,
            colors: AppColors::default(),
        }
    }
    
    fn setup_fonts(&self, ctx: &egui::Context) {
        // For now, we'll use the default fonts with custom sizing
        // In a real implementation, you would include Open Sans font files
        let fonts = egui::FontDefinitions::default();

        // We'll use default fonts for now
        ctx.set_fonts(fonts);
    }
    
    fn setup_style(&self, ctx: &egui::Context) {
        let mut style = (*ctx.style()).clone();

        // Enhanced dark theme colors
        style.visuals.dark_mode = true;
        style.visuals.override_text_color = Some(self.colors.text_primary);
        style.visuals.panel_fill = self.colors.surface;
        style.visuals.window_fill = self.colors.background;
        style.visuals.extreme_bg_color = self.colors.background;
        style.visuals.faint_bg_color = Color32::from_rgb(32, 32, 32);

        // Enhanced widget styling
        style.visuals.widgets.inactive.bg_fill = self.colors.secondary;
        style.visuals.widgets.inactive.weak_bg_fill = Color32::from_rgb(40, 40, 40);
        style.visuals.widgets.inactive.bg_stroke = Stroke::new(1.0, Color32::from_rgb(60, 60, 60));

        style.visuals.widgets.hovered.bg_fill = self.colors.primary;
        style.visuals.widgets.hovered.weak_bg_fill = Color32::from_rgb(80, 120, 160);
        style.visuals.widgets.hovered.bg_stroke = Stroke::new(1.5, self.colors.primary);

        style.visuals.widgets.active.bg_fill = self.colors.accent;
        style.visuals.widgets.active.weak_bg_fill = Color32::from_rgb(200, 150, 0);
        style.visuals.widgets.active.bg_stroke = Stroke::new(2.0, self.colors.accent);

        // Enhanced spacing and typography
        style.spacing.item_spacing = egui::vec2(10.0, 8.0);
        style.spacing.button_padding = egui::vec2(16.0, 10.0);
        style.spacing.indent = 24.0;
        style.spacing.icon_width = 16.0;
        style.spacing.icon_spacing = 8.0;
        style.spacing.tooltip_width = 300.0;

        // Selection colors
        style.visuals.selection.bg_fill = Color32::from_rgba_unmultiplied(64, 120, 192, 100);
        style.visuals.selection.stroke = Stroke::new(1.0, self.colors.primary);

        // Hyperlink colors
        style.visuals.hyperlink_color = self.colors.primary;

        // Error colors
        style.visuals.error_fg_color = self.colors.error;
        style.visuals.warn_fg_color = self.colors.warning;

        // Separator styling
        style.visuals.widgets.noninteractive.bg_stroke = Stroke::new(1.0, Color32::from_rgb(50, 50, 50));

        // Scrollbar styling
        style.visuals.widgets.inactive.bg_fill = Color32::from_rgb(45, 45, 45);

        ctx.set_style(style);

        // Apply additional styling
        self.apply_additional_styling(ctx);
    }

    fn apply_additional_styling(&self, _ctx: &egui::Context) {
        // Additional styling can be applied here
        // For now, we'll keep it simple without custom fonts
    }
}

impl eframe::App for TradingWidgetApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Setup fonts and styling
        self.setup_style(ctx);

        // Update data from shared state
        self.update_from_shared_state();

        // Process messages from bot
        self.process_bot_messages();
        
        // Main UI
        egui::CentralPanel::default().show(ctx, |ui| {
            self.render_main_ui(ui);
        });
        
        // Request repaint for real-time updates
        ctx.request_repaint_after(std::time::Duration::from_millis(100));
    }
}

impl TradingWidgetApp {
    fn update_from_shared_state(&mut self) {
        // Update local data from shared state
        if let Ok(state) = self.shared_state.lock() {
            // Update trading status
            self.trading_active = state.is_trading_active();

            // Update trade count
            self.total_trades = state.get_total_trades();

            // Note: Token data and active trade are accessed directly from shared state
            // in the rendering methods to avoid cloning large data structures
        }
    }

    fn process_bot_messages(&mut self) {
        // This method is now primarily handled by the log processing thread
        // that updates the shared state directly
    }
    
    fn render_main_ui(&mut self, ui: &mut egui::Ui) {
        // Enhanced top bar with gradient background
        ui.allocate_ui_with_layout(
            egui::vec2(ui.available_width(), 60.0),
            egui::Layout::top_down(egui::Align::LEFT),
            |ui| {
                // Background with subtle gradient effect
                let rect = ui.max_rect();
                let gradient_color1 = Color32::from_rgb(25, 25, 30);

                // Simple gradient approximation
                ui.painter().rect_filled(rect, 8.0, gradient_color1);

                ui.horizontal(|ui| {
                    ui.add_space(16.0);

                    // Enhanced title with icon
                    ui.horizontal(|ui| {
                        ui.label(RichText::new("🎯")
                            .size(28.0)
                            .color(self.colors.accent));

                        ui.label(RichText::new("PumpFun Sniper")
                            .size(24.0)
                            .strong()
                            .color(self.colors.text_primary));

                        ui.label(RichText::new("Trading Widget")
                            .size(18.0)
                            .color(self.colors.text_secondary));
                    });

                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        ui.add_space(16.0);

                        // Enhanced emergency stop button with animation
                        let emergency_active = self.shared_state.lock().unwrap().is_emergency_stop();
                        let button_color = if emergency_active {
                            self.colors.error
                        } else {
                            Color32::from_rgb(180, 60, 60)
                        };

                        let emergency_button = egui::Button::new(
                            RichText::new("🚨 EMERGENCY STOP")
                                .size(14.0)
                                .strong()
                                .color(Color32::WHITE)
                        )
                        .fill(button_color)
                        .min_size(egui::vec2(140.0, 35.0));

                        if ui.add(emergency_button).clicked() {
                            self.shared_state.lock().unwrap().trigger_emergency_stop();
                        }

                        ui.add_space(8.0);

                        // Enhanced toggle buttons
                        let terminal_button = egui::Button::new(
                            RichText::new("📟 Terminal")
                                .size(13.0)
                                .color(if self.show_terminal { Color32::WHITE } else { self.colors.text_secondary })
                        )
                        .fill(if self.show_terminal { self.colors.primary } else { Color32::from_rgb(50, 50, 50) })
                        .min_size(egui::vec2(80.0, 30.0));

                        if ui.add(terminal_button).clicked() {
                            self.show_terminal = !self.show_terminal;
                        }

                        let advanced_button = egui::Button::new(
                            RichText::new("⚙️ Advanced")
                                .size(13.0)
                                .color(if self.show_advanced { Color32::WHITE } else { self.colors.text_secondary })
                        )
                        .fill(if self.show_advanced { self.colors.primary } else { Color32::from_rgb(50, 50, 50) })
                        .min_size(egui::vec2(90.0, 30.0));

                        if ui.add(advanced_button).clicked() {
                            self.show_advanced = !self.show_advanced;
                        }
                    });
                });
            }
        );

        ui.add_space(8.0);

        // Enhanced separator with gradient
        let separator_rect = ui.allocate_exact_size(egui::vec2(ui.available_width(), 2.0), egui::Sense::hover()).0;
        ui.painter().rect_filled(
            separator_rect,
            1.0,
            Color32::from_rgb(60, 60, 60)
        );

        ui.add_space(8.0);

        // Main content area with enhanced styling
        if self.show_terminal {
            self.render_enhanced_terminal_panel(ui);
        } else {
            // Enhanced content sections with better spacing
            ui.allocate_ui_with_layout(
                egui::vec2(ui.available_width(), ui.available_height()),
                egui::Layout::top_down(egui::Align::LEFT),
                |ui| {
                    // Token aging pipeline with enhanced styling
                    ui.group(|ui| {
                        ui.label(RichText::new("Token Aging Pipeline")
                            .size(16.0)
                            .strong()
                            .color(Color32::WHITE));
                        ui.separator();
                    });

                    ui.add_space(12.0);

                    // Active trade monitoring with enhanced styling
                    ui.group(|ui| {
                        ui.label(RichText::new("Active Trade Monitor")
                            .size(16.0)
                            .strong()
                            .color(Color32::WHITE));
                        ui.separator();
                    });

                    ui.add_space(12.0);

                    // Environment variable controls with enhanced styling
                    ui.group(|ui| {
                        let title = if self.show_advanced {
                            "Advanced Configuration"
                        } else {
                            "Trading Configuration"
                        };
                        ui.label(RichText::new(title)
                            .size(16.0)
                            .strong()
                            .color(Color32::WHITE));
                        ui.separator();
                    });
                }
            );

            // Render the actual content outside the closure
            self.render_token_pipeline_section(ui);
            ui.add_space(8.0);
            self.render_active_trade_section(ui);
            ui.add_space(8.0);
            if self.show_advanced {
                self.render_advanced_controls(ui);
            } else {
                self.render_basic_controls(ui);
            }
        }
    }



    fn render_enhanced_terminal_panel(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            // Terminal header with enhanced styling
            ui.allocate_ui_with_layout(
                egui::vec2(ui.available_width(), 35.0),
                egui::Layout::left_to_right(egui::Align::Center),
                |ui| {
                    let header_rect = ui.max_rect();
                    ui.painter().rect_filled(
                        header_rect,
                        4.0,
                        Color32::from_rgb(20, 20, 25)
                    );

                    ui.add_space(12.0);
                    ui.label(RichText::new("📟 Terminal Console")
                        .size(18.0)
                        .strong()
                        .color(self.colors.text_primary));

                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        ui.add_space(12.0);

                        // Connection status indicator
                        let status_color = if self.shared_state.lock().unwrap().is_trading_active() {
                            self.colors.success
                        } else {
                            self.colors.error
                        };

                        ui.label(RichText::new("●")
                            .size(16.0)
                            .color(status_color));

                        ui.label(RichText::new("Live")
                            .size(12.0)
                            .color(self.colors.text_secondary));
                    });
                }
            );

            ui.add_space(8.0);

            // Terminal content
            self.render_terminal_panel(ui);
        });
    }
    
    fn render_token_pipeline_section(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            // Header with pipeline status
            ui.horizontal(|ui| {
                ui.label(RichText::new("Token Aging Pipeline")
                    .size(18.0)
                    .color(self.colors.text_primary));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    let shared_state = self.shared_state.lock().unwrap();
                    let token_count = shared_state.aging_tokens.len();
                    let max_tokens = 23; // From env config

                    let status_color = if token_count >= max_tokens {
                        self.colors.warning
                    } else if token_count > max_tokens * 3 / 4 {
                        self.colors.accent
                    } else {
                        self.colors.success
                    };

                    ui.label(RichText::new(&format!("{}/{} tokens", token_count, max_tokens))
                        .color(status_color));
                });
            });

            ui.separator();

            let shared_state = self.shared_state.lock().unwrap();

            if shared_state.aging_tokens.is_empty() {
                ui.vertical_centered(|ui| {
                    ui.add_space(20.0);
                    ui.label(RichText::new("🔍 Scanning for graduation candidates...")
                        .size(16.0)
                        .color(self.colors.text_secondary));
                    ui.label(RichText::new("Tokens will appear here as they enter the aging pipeline")
                        .size(12.0)
                        .color(self.colors.text_secondary));
                    ui.add_space(20.0);
                });
            } else {
                // Sort tokens by progress (highest first)
                let mut tokens: Vec<_> = shared_state.aging_tokens.iter().collect();
                tokens.sort_by(|a, b| b.1.progress.partial_cmp(&a.1.progress).unwrap_or(std::cmp::Ordering::Equal));

                for (_mint, token) in tokens {
                    self.render_enhanced_token_progress_bar(ui, token);
                    ui.add_space(4.0);
                }
            }
        });
    }
    
    fn render_enhanced_token_progress_bar(&self, ui: &mut egui::Ui, token: &TokenInfo) {
        ui.group(|ui| {
            // Token header with key metrics
            ui.horizontal(|ui| {
                // Token name and symbol
                ui.label(RichText::new(&format!("{}", token.name))
                    .size(14.0)
                    .strong()
                    .color(self.colors.text_primary));

                ui.label(RichText::new(&format!("({})", token.symbol))
                    .size(12.0)
                    .color(self.colors.text_secondary));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    // Age indicator
                    let age_minutes = token.age_seconds / 60;
                    let age_color = if age_minutes > 8 { self.colors.warning } else { self.colors.text_secondary };
                    ui.label(RichText::new(&format!("{}m", age_minutes))
                        .color(age_color));

                    // Score display
                    let score_color = if token.score >= token.score_threshold {
                        self.colors.success
                    } else if token.score >= token.score_threshold * 0.8 {
                        self.colors.accent
                    } else {
                        self.colors.text_secondary
                    };

                    ui.label(RichText::new(&format!("Score: {:.3}", token.score))
                        .color(score_color));
                });
            });

            // Enhanced progress bar
            let progress = token.progress.clamp(0.0, 1.0) as f32;
            let bar_width = ui.available_width() - 20.0;
            let bar_height = 24.0;

            let (rect, response) = ui.allocate_exact_size(
                egui::vec2(bar_width, bar_height),
                egui::Sense::hover()
            );

            // Background with subtle pattern
            ui.painter().rect_filled(rect, 6.0, self.colors.background);

            // Progress fill with smooth gradient
            if progress > 0.0 {
                let fill_width = bar_width * progress;
                let fill_rect = egui::Rect::from_min_size(
                    rect.min + egui::vec2(1.0, 1.0),
                    egui::vec2(fill_width - 2.0, bar_height - 2.0)
                );

                // Multi-segment gradient for smoother transition
                let segments = 10;
                let segment_width = fill_width / segments as f32;

                for i in 0..segments {
                    let segment_progress = (i as f32 + 0.5) / segments as f32;
                    let actual_progress = (progress * segments as f32).min(i as f32 + 1.0) - i as f32;

                    if actual_progress <= 0.0 { break; }

                    let color = self.get_gradient_color(segment_progress);
                    let segment_rect = egui::Rect::from_min_size(
                        rect.min + egui::vec2(1.0 + i as f32 * segment_width, 1.0),
                        egui::vec2(segment_width * actual_progress, bar_height - 2.0)
                    );

                    ui.painter().rect_filled(segment_rect, 5.0, color);
                }

                // Highlight effect for high progress
                if progress > 0.8 {
                    let highlight_alpha = ((progress - 0.8) * 5.0).min(1.0);
                    let highlight_color = Color32::from_rgba_unmultiplied(255, 255, 255, (50.0 * highlight_alpha) as u8);
                    ui.painter().rect_filled(fill_rect, 5.0, highlight_color);
                }
            }

            // Progress text overlay
            let text_pos = rect.center() - egui::vec2(0.0, 6.0);
            let progress_text = format!("{:.1}%", progress * 100.0);
            ui.painter().text(
                text_pos,
                egui::Align2::CENTER_CENTER,
                &progress_text,
                egui::FontId::proportional(12.0),
                Color32::WHITE
            );

            // Threshold indicator line
            if token.score_threshold > 0.0 {
                let threshold_progress = (token.score / token.score_threshold).min(1.0) as f32;
                let threshold_x = rect.min.x + bar_width * threshold_progress;
                let threshold_line = egui::Rect::from_min_size(
                    egui::pos2(threshold_x - 1.0, rect.min.y),
                    egui::vec2(2.0, bar_height)
                );
                ui.painter().rect_filled(threshold_line, 0.0, Color32::WHITE);
            }

            // Hover tooltip
            if response.hovered() {
                response.on_hover_ui(|ui| {
                    ui.label(format!("Token: {} ({})", token.name, token.symbol));
                    ui.label(format!("Mint: {}", token.mint));
                    ui.label(format!("Current Score: {:.4}", token.score));
                    ui.label(format!("Threshold: {:.4}", token.score_threshold));
                    ui.label(format!("Age: {}m {}s", token.age_seconds / 60, token.age_seconds % 60));
                    ui.label(format!("Bonding Curve: {:.1}%", token.bonding_curve_progress * 100.0));
                });
            }

            // Sub-metrics row
            ui.horizontal(|ui| {
                ui.small(format!("BC: {:.1}%", token.bonding_curve_progress * 100.0));
                ui.separator();
                ui.small(format!("Threshold: {:.3}", token.score_threshold));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    let time_remaining = (600 - token.age_seconds).max(0); // 10 min max
                    let minutes = time_remaining / 60;
                    let seconds = time_remaining % 60;
                    ui.small(format!("⏱ {}:{:02}", minutes, seconds));
                });
            });
        });
    }

    fn get_gradient_color(&self, progress: f32) -> Color32 {
        let progress = progress.clamp(0.0, 1.0);

        if progress < 0.5 {
            // Red to yellow transition
            let t = progress * 2.0;
            Color32::from_rgb(
                244,
                (67.0 + (255.0 - 67.0) * t) as u8,
                (54.0 * (1.0 - t)) as u8,
            )
        } else {
            // Yellow to green transition
            let t = (progress - 0.5) * 2.0;
            Color32::from_rgb(
                (255.0 * (1.0 - t) + 76.0 * t) as u8,
                (255.0 * (1.0 - t) + 175.0 * t) as u8,
                (0.0 * (1.0 - t) + 80.0 * t) as u8,
            )
        }
    }
    
    fn render_active_trade_section(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            // Header with trade status
            ui.horizontal(|ui| {
                ui.label(RichText::new("Active Trade Monitor")
                    .size(18.0)
                    .color(self.colors.text_primary));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    let shared_state = self.shared_state.lock().unwrap();
                    let total_trades = shared_state.get_total_trades();
                    ui.label(RichText::new(&format!("Total: {}", total_trades))
                        .color(self.colors.text_secondary));

                    // Trading status indicator
                    let (status_text, status_color) = if shared_state.is_trading_active() {
                        ("🟢 ACTIVE", self.colors.success)
                    } else {
                        ("🔴 PAUSED", self.colors.error)
                    };
                    ui.label(RichText::new(status_text).color(status_color));
                });
            });

            ui.separator();

            let shared_state = self.shared_state.lock().unwrap();

            if let Some(trade) = &shared_state.active_trade {
                self.render_enhanced_trade_progress(ui, trade);
            } else {
                ui.vertical_centered(|ui| {
                    ui.add_space(15.0);
                    ui.label(RichText::new("⏳ Waiting for trading opportunity...")
                        .size(16.0)
                        .color(self.colors.text_secondary));
                    ui.label(RichText::new("Active trades will be displayed here with real-time progress")
                        .size(12.0)
                        .color(self.colors.text_secondary));
                    ui.add_space(15.0);
                });
            }
        });
    }
    
    fn render_enhanced_trade_progress(&self, ui: &mut egui::Ui, trade: &ActiveTrade) {
        ui.group(|ui| {
            // Trade header with key information
            ui.horizontal(|ui| {
                // Token information
                ui.vertical(|ui| {
                    ui.label(RichText::new(&format!("{}", trade.token_name))
                        .size(16.0)
                        .strong()
                        .color(self.colors.text_primary));
                    ui.label(RichText::new(&format!("({})", trade.token_symbol))
                        .size(12.0)
                        .color(self.colors.text_secondary));
                });

                ui.separator();

                // Trade metrics
                ui.vertical(|ui| {
                    ui.horizontal(|ui| {
                        ui.label("Entry:");
                        ui.label(RichText::new(&format!("{:.6} SOL", trade.entry_price))
                            .color(self.colors.text_primary));
                    });
                    ui.horizontal(|ui| {
                        ui.label("Current:");
                        ui.label(RichText::new(&format!("{:.6} SOL", trade.current_price))
                            .color(self.colors.text_primary));
                    });
                });

                ui.separator();

                // PnL and status
                ui.vertical(|ui| {
                    let pnl_color = if trade.pnl_percent >= 0.0 {
                        self.colors.success
                    } else {
                        self.colors.error
                    };

                    ui.horizontal(|ui| {
                        ui.label("PnL:");
                        ui.label(RichText::new(&format!("{:+.2}%", trade.pnl_percent))
                            .size(14.0)
                            .strong()
                            .color(pnl_color));
                    });

                    let (status_text, status_color) = match trade.status {
                        TradeStatus::Buying => ("🔄 Buying", self.colors.accent),
                        TradeStatus::Holding => ("📈 Holding", self.colors.success),
                        TradeStatus::Selling => ("💰 Selling", self.colors.warning),
                        TradeStatus::Completed => ("✅ Completed", self.colors.success),
                        TradeStatus::Failed => ("❌ Failed", self.colors.error),
                    };

                    ui.label(RichText::new(status_text)
                        .color(status_color));
                });

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    // Position size
                    ui.vertical(|ui| {
                        ui.label("Position:");
                        ui.label(RichText::new(&format!("{:.4} SOL", trade.position_size))
                            .color(self.colors.text_primary));
                    });
                });
            });

            ui.separator();

            // Enhanced progress bar for trade lifecycle
            self.render_trade_lifecycle_bar(ui, trade);

            // Trade timeline
            ui.horizontal(|ui| {
                let elapsed = chrono::Utc::now().signed_duration_since(trade.trade_start_time);
                let elapsed_seconds = elapsed.num_seconds();
                let minutes = elapsed_seconds / 60;
                let seconds = elapsed_seconds % 60;

                ui.label(format!("Duration: {}:{:02}", minutes, seconds));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    // Expected completion time based on status
                    let expected_duration = match trade.status {
                        TradeStatus::Buying => "~30s",
                        TradeStatus::Holding => "~5-10m",
                        TradeStatus::Selling => "~30s",
                        _ => "Complete",
                    };
                    ui.label(format!("Expected: {}", expected_duration));
                });
            });
        });
    }

    fn render_trade_lifecycle_bar(&self, ui: &mut egui::Ui, trade: &ActiveTrade) {
        let bar_width = ui.available_width() - 20.0;
        let bar_height = 28.0;

        let (rect, response) = ui.allocate_exact_size(
            egui::vec2(bar_width, bar_height),
            egui::Sense::hover()
        );

        // Background
        ui.painter().rect_filled(rect, 8.0, self.colors.background);

        // Trade lifecycle stages
        let stages = ["Entry", "Holding", "Exit"];
        let stage_width = bar_width / stages.len() as f32;

        // Determine current stage progress
        let (current_stage, stage_progress) = match trade.status {
            TradeStatus::Buying => (0, trade.progress),
            TradeStatus::Holding => (1, trade.progress),
            TradeStatus::Selling => (2, trade.progress),
            TradeStatus::Completed => (2, 1.0),
            TradeStatus::Failed => (0, 0.0), // Show as failed at entry
        };

        // Draw stage backgrounds and progress
        for (i, stage_name) in stages.iter().enumerate() {
            let stage_rect = egui::Rect::from_min_size(
                rect.min + egui::vec2(i as f32 * stage_width + 2.0, 2.0),
                egui::vec2(stage_width - 4.0, bar_height - 4.0)
            );

            // Stage background color
            let stage_color = if i < current_stage {
                self.colors.success // Completed stages
            } else if i == current_stage {
                if trade.status == TradeStatus::Failed {
                    self.colors.error
                } else {
                    self.colors.primary // Current stage
                }
            } else {
                self.colors.surface // Future stages
            };

            // Fill stage background
            ui.painter().rect_filled(stage_rect, 6.0, stage_color);

            // Progress fill for current stage
            if i == current_stage && stage_progress > 0.0 && trade.status != TradeStatus::Failed {
                let progress_width = (stage_width - 4.0) * stage_progress as f32;
                let progress_rect = egui::Rect::from_min_size(
                    stage_rect.min,
                    egui::vec2(progress_width, stage_rect.height())
                );

                // Animated progress color
                let progress_color = if trade.status == TradeStatus::Holding {
                    // Pulse effect for holding
                    let pulse = (chrono::Utc::now().timestamp_millis() % 2000) as f32 / 2000.0;
                    let alpha = (0.7 + 0.3 * (pulse * std::f32::consts::PI * 2.0).sin()) as u8;
                    Color32::from_rgba_unmultiplied(
                        self.colors.accent.r(),
                        self.colors.accent.g(),
                        self.colors.accent.b(),
                        alpha
                    )
                } else {
                    self.colors.accent
                };

                ui.painter().rect_filled(progress_rect, 6.0, progress_color);
            }

            // Stage label
            let text_pos = stage_rect.center();
            ui.painter().text(
                text_pos,
                egui::Align2::CENTER_CENTER,
                stage_name,
                egui::FontId::proportional(11.0),
                Color32::WHITE
            );

            // Stage separator
            if i < stages.len() - 1 {
                let sep_x = rect.min.x + (i + 1) as f32 * stage_width;
                ui.painter().line_segment(
                    [egui::pos2(sep_x, rect.min.y + 4.0), egui::pos2(sep_x, rect.max.y - 4.0)],
                    Stroke::new(1.0, self.colors.text_secondary)
                );
            }
        }

        // Hover tooltip
        if response.hovered() {
            response.on_hover_ui(|ui| {
                ui.label(format!("Token: {} ({})", trade.token_name, trade.token_symbol));
                ui.label(format!("Entry Price: {:.6} SOL", trade.entry_price));
                ui.label(format!("Current Price: {:.6} SOL", trade.current_price));
                ui.label(format!("Position Size: {:.4} SOL", trade.position_size));
                ui.label(format!("PnL: {:+.2}%", trade.pnl_percent));
                ui.label(format!("Status: {:?}", trade.status));
                ui.label(format!("Started: {}", trade.trade_start_time.format("%H:%M:%S")));
            });
        }
    }
    
    fn render_basic_controls(&mut self, ui: &mut egui::Ui) {
        // 3-column layout for basic controls
        ui.columns(3, |columns| {
            // Left column: Token Scoring Thresholds and Scoring Parameters
            columns[0].group(|ui| {
                self.render_env_group(ui, &EnvVarGroup::TokenScoringThresholds);
                ui.separator();
                self.render_env_group(ui, &EnvVarGroup::ScoringParameters);
            });
            
            // Center column: Token Scoring Weights (pie chart) and Bonding Curve Monitoring
            columns[1].group(|ui| {
                self.render_scoring_weights_pie_chart(ui);
                ui.separator();
                self.render_env_group(ui, &EnvVarGroup::BondingCurveMonitoring);
            });
            
            // Right column: Token Aging Pipeline and Token Filtering Thresholds
            columns[2].group(|ui| {
                self.render_env_group(ui, &EnvVarGroup::TokenAgingPipeline);
                ui.separator();
                self.render_env_group(ui, &EnvVarGroup::TokenFilteringThresholds);
            });
        });
    }
    
    fn render_env_group(&mut self, ui: &mut egui::Ui, group: &EnvVarGroup) {
        let group_name = match group {
            EnvVarGroup::TokenScoringThresholds => "Token Scoring Thresholds",
            EnvVarGroup::ScoringParameters => "Scoring Parameters",
            EnvVarGroup::TokenAgingPipeline => "Token Aging Pipeline",
            EnvVarGroup::TokenFilteringThresholds => "Token Filtering Thresholds",
            EnvVarGroup::BondingCurveMonitoring => "Bonding Curve Monitoring",
            _ => "Other Settings",
        };

        ui.label(RichText::new(group_name)
            .size(16.0)
            .color(self.colors.text_primary));

        // Collect variable data to avoid borrowing issues
        let variables: Vec<_> = self.env_manager.get_variables_by_group(group)
            .into_iter()
            .map(|var| (var.key.clone(), var.value.clone()))
            .collect();

        let mut updates = Vec::new();

        for (var_key, var_value) in variables {
            ui.horizontal(|ui| {
                ui.label(&var_key);

                let current_value = self.env_edit_values.get(&var_key)
                    .unwrap_or(&var_value)
                    .clone();

                let mut new_value = current_value;
                let response = ui.text_edit_singleline(&mut new_value);

                if response.changed() {
                    updates.push((var_key.clone(), new_value));
                }
            });

            // Show validation error if any
            if let Some(error) = self.env_validation_errors.get(&var_key) {
                ui.label(RichText::new(error).color(self.colors.error));
            }
        }

        // Process updates after the UI loop
        for (var_key, new_value) in updates {
            self.env_edit_values.insert(var_key.clone(), new_value.clone());
            // Validate and update
            if let Err(e) = self.env_manager.update_variable(&var_key, &new_value) {
                self.env_validation_errors.insert(var_key.clone(), e.to_string());
            } else {
                self.env_validation_errors.remove(&var_key);
            }
        }
    }
    
    fn render_scoring_weights_pie_chart(&mut self, ui: &mut egui::Ui) {
        ui.vertical(|ui| {
            ui.label(RichText::new("Token Scoring Weights")
                .size(16.0)
                .color(self.colors.text_primary));

            // Get the weight values
            let survival_weight = self.env_edit_values.get("PUMP_SURVIVAL_SCORE_WEIGHT")
                .and_then(|v| v.parse::<f64>().ok())
                .unwrap_or(0.15);
            let momentum_weight = self.env_edit_values.get("PUMP_MOMENTUM_SCORE_WEIGHT")
                .and_then(|v| v.parse::<f64>().ok())
                .unwrap_or(0.20);
            let graduation_weight = self.env_edit_values.get("PUMP_GRADUATION_SCORE_WEIGHT")
                .and_then(|v| v.parse::<f64>().ok())
                .unwrap_or(0.40);
            let risk_weight = self.env_edit_values.get("PUMP_RISK_SCORE_WEIGHT")
                .and_then(|v| v.parse::<f64>().ok())
                .unwrap_or(0.25);

            let weights = vec![
                ("Survival", survival_weight, self.colors.success),
                ("Momentum", momentum_weight, self.colors.primary),
                ("Graduation", graduation_weight, self.colors.accent),
                ("Risk", risk_weight, self.colors.error),
            ];

            let total = weights.iter().map(|(_, w, _)| w).sum::<f64>();

            // Interactive pie chart
            let chart_size = 120.0;
            let (rect, response) = ui.allocate_exact_size(
                egui::vec2(chart_size, chart_size),
                egui::Sense::hover()
            );

            let center = rect.center();
            let radius = chart_size / 2.0 - 10.0;

            // Draw pie chart
            let mut start_angle = 0.0;
            let mut hovered_segment = None;

            for (i, (name, weight, color)) in weights.iter().enumerate() {
                let normalized_weight = if total > 0.0 { weight / total } else { 0.25 };
                let angle = (normalized_weight * 2.0 * std::f64::consts::PI) as f32;

                if angle > 0.01 { // Only draw if significant
                    // Check if mouse is hovering over this segment
                    if response.hovered() {
                        if let Some(mouse_pos) = ui.ctx().pointer_latest_pos() {
                            let mouse_angle = (mouse_pos.y - center.y).atan2(mouse_pos.x - center.x);
                            let mouse_angle = if mouse_angle < 0.0 { mouse_angle + 2.0 * std::f32::consts::PI } else { mouse_angle };
                            let end_angle = start_angle + angle as f32;

                            if mouse_angle >= start_angle && mouse_angle <= end_angle {
                                let distance = ((mouse_pos.x - center.x).powi(2) + (mouse_pos.y - center.y).powi(2)).sqrt();
                                if distance <= radius {
                                    hovered_segment = Some(i);
                                }
                            }
                        }
                    }

                    // Draw segment
                    let segment_radius = if hovered_segment == Some(i) { radius + 5.0 } else { radius };
                    self.draw_pie_segment(ui, center, segment_radius, start_angle, angle as f32, *color);

                    start_angle += angle as f32;
                }
            }

            // Draw center circle
            ui.painter().circle_filled(center, radius * 0.4, self.colors.background);
            ui.painter().circle_stroke(center, radius * 0.4, Stroke::new(2.0, self.colors.secondary));

            // Total percentage in center
            let total_text = if (total - 1.0).abs() > 0.01 {
                format!("{:.0}%", total * 100.0)
            } else {
                "100%".to_string()
            };

            let total_color = if (total - 1.0).abs() > 0.01 {
                self.colors.warning
            } else {
                self.colors.success
            };

            ui.painter().text(
                center,
                egui::Align2::CENTER_CENTER,
                &total_text,
                egui::FontId::proportional(14.0),
                total_color
            );

            // Legend with interactive controls
            ui.add_space(10.0);

            let mut weight_updates = Vec::new();

            for (i, (name, weight, color)) in weights.iter().enumerate() {
                ui.horizontal(|ui| {
                    // Color indicator
                    let color_rect = ui.allocate_exact_size(egui::vec2(12.0, 12.0), egui::Sense::hover()).0;
                    ui.painter().rect_filled(color_rect, 2.0, *color);

                    // Weight name
                    ui.label(format!("{}:", name));

                    // Editable weight value
                    let mut weight_str = format!("{:.3}", weight);
                    let response = ui.add(egui::TextEdit::singleline(&mut weight_str).desired_width(60.0));

                    if response.changed() {
                        if let Ok(new_weight) = weight_str.parse::<f64>() {
                            let env_key = match i {
                                0 => "PUMP_SURVIVAL_SCORE_WEIGHT",
                                1 => "PUMP_MOMENTUM_SCORE_WEIGHT",
                                2 => "PUMP_GRADUATION_SCORE_WEIGHT",
                                3 => "PUMP_RISK_SCORE_WEIGHT",
                                _ => "",
                            };
                            weight_updates.push((env_key.to_string(), new_weight.to_string()));
                        }
                    }

                    // Percentage display
                    let percentage = if total > 0.0 { (weight / total) * 100.0 } else { 0.0 };
                    ui.label(format!("({:.1}%)", percentage));
                });
            }

            // Apply weight updates
            for (key, value) in weight_updates {
                self.env_edit_values.insert(key.clone(), value.clone());
                if let Err(e) = self.env_manager.update_variable(&key, &value) {
                    self.env_validation_errors.insert(key, e.to_string());
                }
            }

            // Validation message
            if (total - 1.0).abs() > 0.01 {
                ui.label(RichText::new(format!("⚠️ Total: {:.1}% (should be 100.0%)", total * 100.0))
                    .color(self.colors.warning));

                if ui.button("Auto-Normalize").clicked() {
                    self.normalize_weights();
                }
            } else {
                ui.label(RichText::new("✅ Weights properly normalized")
                    .color(self.colors.success));
            }

            // Hover tooltip
            if let Some(segment_idx) = hovered_segment {
                if let Some((name, weight, _)) = weights.get(segment_idx) {
                    // Simple tooltip replacement - show info in a label for now
                    ui.label(format!("Hover: {}: {:.1}%", name, (weight / total.max(0.001)) * 100.0));
                }
            }
        });
    }

    fn draw_pie_segment(&self, ui: &mut egui::Ui, center: egui::Pos2, radius: f32, start_angle: f32, angle: f32, color: Color32) {
        let steps = (angle * 20.0).max(3.0) as usize;
        let mut points = vec![center];

        for i in 0..=steps {
            let current_angle = start_angle + (i as f32 / steps as f32) * angle;
            let x = center.x + radius * current_angle.cos();
            let y = center.y + radius * current_angle.sin();
            points.push(egui::pos2(x, y));
        }

        // Draw filled segment
        ui.painter().add(egui::epaint::Shape::convex_polygon(
            points,
            color,
            Stroke::new(1.0, self.colors.background)
        ));
    }

    fn normalize_weights(&mut self) {
        let weights = vec![
            ("PUMP_SURVIVAL_SCORE_WEIGHT", 0.15),
            ("PUMP_MOMENTUM_SCORE_WEIGHT", 0.20),
            ("PUMP_GRADUATION_SCORE_WEIGHT", 0.40),
            ("PUMP_RISK_SCORE_WEIGHT", 0.25),
        ];

        for (key, default_value) in weights {
            let value_str = format!("{:.3}", default_value);
            self.env_edit_values.insert(key.to_string(), value_str.clone());
            let _ = self.env_manager.update_variable(key, &value_str);
            self.env_validation_errors.remove(key);
        }
    }
    
    fn render_advanced_controls(&mut self, ui: &mut egui::Ui) {
        ui.vertical(|ui| {
            ui.horizontal(|ui| {
                ui.label(RichText::new("Advanced Settings")
                    .size(18.0)
                    .color(self.colors.text_primary));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    if ui.button("Reset to Defaults").clicked() {
                        self.reset_to_defaults();
                    }

                    if ui.button("Save Configuration").clicked() {
                        let _ = self.env_manager.save_to_file();
                    }
                });
            });

            ui.separator();

            // Group variables by category for better organization
            egui::ScrollArea::vertical()
                .max_height(400.0)
                .show(ui, |ui| {
                    self.render_advanced_group(ui, &EnvVarGroup::WalletConfiguration);
                    self.render_advanced_group(ui, &EnvVarGroup::TradingSettings);
                    self.render_advanced_group(ui, &EnvVarGroup::SafetyLimits);
                    self.render_advanced_group(ui, &EnvVarGroup::TradeExecution);
                    self.render_advanced_group(ui, &EnvVarGroup::Monitoring);
                    self.render_advanced_group(ui, &EnvVarGroup::RateLimiting);
                    self.render_advanced_group(ui, &EnvVarGroup::ChainStackInfrastructure);
                    self.render_advanced_group(ui, &EnvVarGroup::TestingConfiguration);
                });
        });
    }

    fn render_advanced_group(&mut self, ui: &mut egui::Ui, group: &EnvVarGroup) {
        let group_name = self.get_group_display_name(group);

        ui.collapsing(RichText::new(group_name).size(16.0).color(self.colors.text_primary), |ui| {
            let variables = self.env_manager.get_variables_by_group(group);

            if variables.is_empty() {
                ui.label(RichText::new("No variables in this group")
                    .color(self.colors.text_secondary));
                return;
            }

            let mut updates = Vec::new();

            for var in variables {
                ui.group(|ui| {
                    // Variable header
                    ui.horizontal(|ui| {
                        ui.label(RichText::new(&var.key)
                            .size(13.0)
                            .strong()
                            .color(self.colors.text_primary));

                        // Type indicator
                        let type_text = match var.var_type {
                            EnvVarType::String => "Text",
                            EnvVarType::Integer => "Number",
                            EnvVarType::Float => "Decimal",
                            EnvVarType::Boolean => "True/False",
                            EnvVarType::Percentage => "Percent",
                            EnvVarType::Duration => "Time",
                            EnvVarType::Currency => "SOL",
                            EnvVarType::Address => "Address",
                        };

                        ui.label(RichText::new(&format!("[{}]", type_text))
                            .size(10.0)
                            .color(self.colors.accent));
                    });

                    // Description
                    if !var.description.is_empty() {
                        ui.label(RichText::new(&var.description)
                            .size(11.0)
                            .color(self.colors.text_secondary));
                    }

                    // Value input
                    ui.horizontal(|ui| {
                        let current_value = self.env_edit_values.get(&var.key)
                            .unwrap_or(&var.value)
                            .clone();

                        if var.is_sensitive {
                            self.render_sensitive_input(ui, &var.key, &current_value, &mut updates);
                        } else {
                            self.render_regular_input(ui, &var.key, &current_value, &var.var_type, &mut updates);
                        }
                    });

                    // Validation info
                    if let Some(validation) = &var.validation {
                        ui.horizontal(|ui| {
                            ui.label("Constraints:");

                            if let Some(min) = validation.min_value {
                                ui.label(format!("Min: {}", min));
                            }
                            if let Some(max) = validation.max_value {
                                ui.label(format!("Max: {}", max));
                            }
                            if validation.required {
                                ui.label(RichText::new("Required").color(self.colors.warning));
                            }
                        });
                    }

                    // Show validation error if any
                    if let Some(error) = self.env_validation_errors.get(&var.key) {
                        ui.label(RichText::new(&format!("❌ {}", error))
                            .color(self.colors.error));
                    }
                });

                ui.add_space(5.0);
            }

            // Apply updates
            for (key, value) in updates {
                self.env_edit_values.insert(key.clone(), value.clone());
                if let Err(e) = self.env_manager.update_variable(&key, &value) {
                    self.env_validation_errors.insert(key, e.to_string());
                }
            }
        });

        ui.add_space(10.0);
    }

    fn render_sensitive_input(&self, ui: &mut egui::Ui, key: &str, current_value: &str, updates: &mut Vec<(String, String)>) {
        ui.label("Value:");

        // Show masked value
        let masked_display = if current_value.is_empty() {
            "Not set".to_string()
        } else {
            "*".repeat(current_value.len().min(20))
        };

        ui.label(RichText::new(&masked_display).color(self.colors.text_secondary));

        // Secure input options
        if ui.button("📋 Paste from Clipboard").clicked() {
            // In a real implementation, this would securely paste from clipboard
            if let Ok(clipboard_content) = arboard::Clipboard::new().and_then(|mut cb| cb.get_text()) {
                updates.push((key.to_string(), clipboard_content));
            }
        }

        if ui.button("🔑 Enter Securely").clicked() {
            // In a real implementation, this would open a secure input dialog
            // For now, we'll just show a placeholder
        }

        if !current_value.is_empty() && ui.button("🗑 Clear").clicked() {
            updates.push((key.to_string(), String::new()));
        }
    }

    fn render_regular_input(&self, ui: &mut egui::Ui, key: &str, current_value: &str, var_type: &EnvVarType, updates: &mut Vec<(String, String)>) {
        ui.label("Value:");

        let mut new_value = current_value.to_string();

        match var_type {
            EnvVarType::Boolean => {
                let mut is_true = current_value.parse::<bool>().unwrap_or(false);
                if ui.checkbox(&mut is_true, "").changed() {
                    updates.push((key.to_string(), is_true.to_string()));
                }
            }
            EnvVarType::Integer | EnvVarType::Duration => {
                let response = ui.add(egui::DragValue::new(&mut new_value.parse::<i64>().unwrap_or(0))
                    .speed(1.0));
                if response.changed() {
                    updates.push((key.to_string(), new_value));
                }
            }
            EnvVarType::Float | EnvVarType::Percentage | EnvVarType::Currency => {
                let mut float_val = new_value.parse::<f64>().unwrap_or(0.0);
                let response = ui.add(egui::DragValue::new(&mut float_val)
                    .speed(0.001)
                    .fixed_decimals(4));
                if response.changed() {
                    updates.push((key.to_string(), float_val.to_string()));
                }
            }
            _ => {
                let response = ui.text_edit_singleline(&mut new_value);
                if response.changed() {
                    updates.push((key.to_string(), new_value));
                }
            }
        }
    }

    fn get_group_display_name(&self, group: &EnvVarGroup) -> &'static str {
        match group {
            EnvVarGroup::WalletConfiguration => "🔐 Wallet & API Configuration",
            EnvVarGroup::TradingSettings => "📈 Trading Settings",
            EnvVarGroup::SafetyLimits => "🛡️ Safety Limits",
            EnvVarGroup::TradeExecution => "⚡ Trade Execution",
            EnvVarGroup::Monitoring => "📊 Monitoring",
            EnvVarGroup::RateLimiting => "⏱️ Rate Limiting",
            EnvVarGroup::ChainStackInfrastructure => "🔗 ChainStack Infrastructure",
            EnvVarGroup::TestingConfiguration => "🧪 Testing Configuration",
            _ => "Other Settings",
        }
    }

    fn reset_to_defaults(&mut self) {
        // Collect keys first to avoid borrowing issues
        let keys: Vec<String> = self.env_manager.variables.keys().cloned().collect();

        // Reset all values to their defaults
        for key in keys {
            let default_value = match key.as_str() {
                "PUMP_MIN_SCORE_AGED_CANDIDATES" => "0.3",
                "PUMP_MIN_SCORE_FRESH_CANDIDATES" => "0.35",
                "PUMP_MIN_TOTAL_SCORE" => "0.7",
                "PUMP_MIN_SURVIVAL_SCORE" => "0.4",
                "PUMP_MIN_MOMENTUM_SCORE" => "0.3",
                "PUMP_MIN_GRADUATION_SCORE" => "0.5",
                "PUMP_MAX_RISK_SCORE" => "0.8",
                "PUMP_AGING_PIPELINE_MAX_TRACKED_TOKENS" => "23",
                "PUMP_AGING_PIPELINE_CHECK_INTERVAL_SECONDS" => "5",
                "PUMP_AGING_PIPELINE_MAX_AGE_SECONDS" => "600",
                "PUMP_MIN_MARKET_CAP_USD" => "3000",
                "PUMP_MAX_MARKET_CAP_USD" => "************",
                "PUMP_MIN_LIQUIDITY_SOL" => "40.0",
                "PUMP_SURVIVAL_SCORE_WEIGHT" => "0.15",
                "PUMP_MOMENTUM_SCORE_WEIGHT" => "0.20",
                "PUMP_GRADUATION_SCORE_WEIGHT" => "0.40",
                "PUMP_RISK_SCORE_WEIGHT" => "0.25",
                "PUMP_BONDING_CURVE_SCAN_INTERVAL" => "120",
                "PUMP_MAX_TRACKED_TOKENS" => "23",
                _ => "0",
            };

            self.env_edit_values.insert(key.clone(), default_value.to_string());
            let _ = self.env_manager.update_variable(&key, default_value);
        }

        // Clear validation errors
        self.env_validation_errors.clear();
    }
    
    fn render_terminal_panel(&mut self, ui: &mut egui::Ui) {
        ui.vertical(|ui| {
            // Terminal header with controls
            ui.horizontal(|ui| {
                ui.label(RichText::new("Terminal Logs")
                    .size(18.0)
                    .color(self.colors.text_primary));

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    // Log statistics
                    let stats = self.log_capture.get_statistics();
                    ui.label(format!("Total: {}", stats.total_entries));

                    if stats.error_count > 0 {
                        ui.label(RichText::new(&format!("Errors: {}", stats.error_count))
                            .color(self.colors.error));
                    }
                    if stats.warning_count > 0 {
                        ui.label(RichText::new(&format!("Warnings: {}", stats.warning_count))
                            .color(self.colors.warning));
                    }
                });
            });

            ui.separator();

            // Enhanced terminal controls
            ui.horizontal(|ui| {
                // Search functionality
                ui.label("Search:");
                let search_response = ui.add(egui::TextEdit::singleline(&mut self.terminal_search_query)
                    .hint_text("Enter search term or regex..."));

                if search_response.changed() || ui.button("🔍").clicked() {
                    let _ = self.log_capture.search(&self.terminal_search_query);
                }

                if ui.button("Clear Search").clicked() {
                    self.terminal_search_query.clear();
                    self.log_capture.clear_search();
                }

                ui.separator();

                // Log level filter
                ui.label("Level:");
                egui::ComboBox::from_id_salt("log_level_filter")
                    .selected_text("All")
                    .show_ui(ui, |ui| {
                        ui.selectable_value(&mut (), (), "All");
                        ui.selectable_value(&mut (), (), "Error");
                        ui.selectable_value(&mut (), (), "Warning");
                        ui.selectable_value(&mut (), (), "Info");
                        ui.selectable_value(&mut (), (), "Debug");
                    });

                ui.separator();

                // Action buttons
                if ui.button("📋 Copy All").clicked() {
                    self.copy_all_logs();
                }

                if ui.button("🗑 Clear Logs").clicked() {
                    self.log_capture.clear_logs();
                }

                if ui.button("💾 Export").clicked() {
                    self.export_logs_with_dialog();
                }

                // Auto-scroll toggle
                ui.checkbox(&mut self.terminal_auto_scroll, "Auto-scroll");
            });

            ui.separator();

            // Enhanced log display with selection
            let available_height = ui.available_height() - 40.0;

            egui::ScrollArea::vertical()
                .max_height(available_height)
                .auto_shrink([false; 2])
                .stick_to_bottom(self.terminal_auto_scroll)
                .show(ui, |ui| {
                    let entries = self.log_capture.get_filtered_entries();
                    let search_results = self.log_capture.get_search_results();

                    if entries.is_empty() {
                        ui.vertical_centered(|ui| {
                            ui.add_space(50.0);
                            ui.label(RichText::new("No log entries available")
                                .size(16.0)
                                .color(self.colors.text_secondary));
                            ui.label(RichText::new("Logs will appear here as the bot operates")
                                .size(12.0)
                                .color(self.colors.text_secondary));
                        });
                        return;
                    }

                    // Collect context menu actions to avoid borrowing issues
                    let mut context_actions: Vec<String> = Vec::new();

                    for (i, entry) in entries.iter().enumerate() {
                        let is_search_match = search_results.contains(&i);

                        // Log entry container
                        let entry_response = ui.group(|ui| {
                            ui.horizontal(|ui| {
                                // Timestamp
                                ui.label(RichText::new(&entry.timestamp.format("%H:%M:%S%.3f").to_string())
                                    .size(10.0)
                                    .color(self.colors.text_secondary));

                                // Log level with color coding
                                let (level_color, level_icon) = match entry.level.as_str() {
                                    "ERROR" => (self.colors.error, "❌"),
                                    "WARN" => (self.colors.warning, "⚠️"),
                                    "INFO" => (self.colors.primary, "ℹ️"),
                                    "DEBUG" => (self.colors.text_secondary, "🐛"),
                                    "TRACE" => (self.colors.text_secondary, "🔍"),
                                    _ => (self.colors.text_secondary, "📝"),
                                };

                                ui.label(RichText::new(&format!("{} {}", level_icon, entry.level))
                                    .color(level_color)
                                    .size(11.0));

                                // Module name if available
                                if let Some(module) = &entry.module {
                                    ui.label(RichText::new(&format!("[{}]", module))
                                        .size(10.0)
                                        .color(self.colors.accent));
                                }

                                // Message with search highlighting
                                let message_color = if is_search_match {
                                    self.colors.accent
                                } else {
                                    self.colors.text_primary
                                };

                                ui.label(RichText::new(&entry.message)
                                    .color(message_color)
                                    .size(11.0));
                            });
                        });

                        // Context menu for log entries (simplified to avoid borrowing issues)
                        if entry_response.response.clicked() {
                            // Simple click action - copy the line
                            self.copy_log_entry(entry);
                        }

                        // Highlight search matches
                        if is_search_match {
                            let highlight_rect = entry_response.response.rect;
                            ui.painter().rect_filled(
                                highlight_rect,
                                2.0,
                                Color32::from_rgba_unmultiplied(
                                    self.colors.accent.r(),
                                    self.colors.accent.g(),
                                    self.colors.accent.b(),
                                    30
                                )
                            );
                        }
                    }

                    // Apply any context actions
                    for action in context_actions {
                        match action {
                            // Handle actions here if needed
                            _ => {}
                        }
                    }
                });

            // Status bar
            ui.separator();
            ui.horizontal(|ui| {
                let entries = self.log_capture.get_filtered_entries();
                ui.label(format!("Showing {} entries", entries.len()));

                let search_results = self.log_capture.get_search_results();
                if !search_results.is_empty() {
                    ui.label(format!("({} matches)", search_results.len()));
                }

                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    if self.terminal_auto_scroll {
                        ui.label(RichText::new("🔄 Auto-scrolling")
                            .color(self.colors.success));
                    }

                    // Memory usage indicator
                    let stats = self.log_capture.get_statistics();
                    let memory_usage = (stats.total_entries as f32 / 1000.0 * 100.0) as u32;
                    ui.label(format!("Memory: {}%", memory_usage.min(100)));
                });
            });
        });
    }

    fn copy_log_entry(&self, entry: &super::shared_state::LogEntry) {
        let log_line = format!(
            "[{}] [{}] {}{}",
            entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f"),
            entry.level,
            entry.module.as_ref().map_or(String::new(), |m| format!("[{}] ", m)),
            entry.message
        );

        if let Ok(mut clipboard) = arboard::Clipboard::new() {
            let _ = clipboard.set_text(log_line);
        }
    }

    fn copy_timestamp(&self, entry: &super::shared_state::LogEntry) {
        let timestamp = entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f").to_string();

        if let Ok(mut clipboard) = arboard::Clipboard::new() {
            let _ = clipboard.set_text(timestamp);
        }
    }

    fn copy_all_logs(&self) {
        let entries = self.log_capture.get_filtered_entries();
        let all_logs = entries.iter()
            .map(|entry| format!(
                "[{}] [{}] {}{}",
                entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f"),
                entry.level,
                entry.module.as_ref().map_or(String::new(), |m| format!("[{}] ", m)),
                entry.message
            ))
            .collect::<Vec<_>>()
            .join("\n");

        if let Ok(mut clipboard) = arboard::Clipboard::new() {
            let _ = clipboard.set_text(all_logs);
        }
    }

    fn export_logs_with_dialog(&self) {
        // In a real implementation, this would use rfd to open a file dialog
        // For now, we'll export to a timestamped file
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let filename = format!("pumpfun_trading_logs_{}.txt", timestamp);

        if let Err(e) = self.log_capture.export_to_file(&filename) {
            eprintln!("Failed to export logs: {}", e);
        } else {
            println!("Logs exported to: {}", filename);
        }
    }
}
