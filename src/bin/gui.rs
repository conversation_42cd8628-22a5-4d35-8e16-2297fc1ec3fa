//! Main GUI Implementation
//! 
//! This module contains the main GUI application using egui with dark theming,
//! environment variable controls, and real-time monitoring capabilities.

use eframe::egui;
use egui::{Color32, RichText, Stroke};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

use super::shared_state::{SharedState, TokenInfo, ActiveTrade, TradeStatus};
use super::env_manager::{EnvManager, EnvVarGroup};
use super::log_capture::{LogCapture, LogCaptureConfig, LogLevel};

/// Main GUI application
pub struct TradingWidgetApp {
    shared_state: Arc<Mutex<SharedState>>,
    env_manager: EnvManager,
    log_capture: LogCapture,
    
    // UI State
    show_advanced: bool,
    show_terminal: bool,
    selected_tab: MainTab,
    
    // Environment variable editing
    env_edit_values: HashMap<String, String>,
    env_validation_errors: HashMap<String, String>,
    
    // Terminal state
    terminal_scroll_to_bottom: bool,
    terminal_search_query: String,
    terminal_auto_scroll: bool,
    
    // Colors and styling
    colors: AppColors,
}

#[derive(Debug, <PERSON>lone, PartialEq)]
enum MainTab {
    Controls,
    Monitoring,
    Terminal,
}

struct AppColors {
    background: Color32,
    surface: Color32,
    primary: Color32,
    secondary: Color32,
    accent: Color32,
    text_primary: Color32,
    text_secondary: Color32,
    success: Color32,
    warning: Color32,
    error: Color32,
    progress_start: Color32,
    progress_middle: Color32,
    progress_end: Color32,
}

impl Default for AppColors {
    fn default() -> Self {
        Self {
            background: Color32::from_rgb(18, 18, 18),      // Very dark background
            surface: Color32::from_rgb(28, 28, 28),         // Dark surface
            primary: Color32::from_rgb(64, 120, 192),       // Blue primary
            secondary: Color32::from_rgb(96, 96, 96),       // Gray secondary
            accent: Color32::from_rgb(255, 193, 7),         // Amber accent
            text_primary: Color32::from_rgb(255, 255, 255), // White text
            text_secondary: Color32::from_rgb(180, 180, 180), // Light gray text
            success: Color32::from_rgb(76, 175, 80),        // Green
            warning: Color32::from_rgb(255, 152, 0),        // Orange
            error: Color32::from_rgb(244, 67, 54),          // Red
            progress_start: Color32::from_rgb(244, 67, 54), // Red start
            progress_middle: Color32::from_rgb(255, 193, 7), // Yellow middle
            progress_end: Color32::from_rgb(76, 175, 80),   // Green end
        }
    }
}

impl TradingWidgetApp {
    pub fn new(shared_state: Arc<Mutex<SharedState>>) -> Self {
        // Initialize environment manager
        let env_manager = EnvManager::new(".env").unwrap_or_else(|e| {
            eprintln!("Failed to load environment manager: {}", e);
            // Create a minimal env manager for testing
            EnvManager::new("test.env").unwrap()
        });
        
        // Initialize log capture
        let log_config = LogCaptureConfig {
            max_entries: 1000,
            capture_enabled: true,
            auto_scroll: true,
            filter_level: LogLevel::Info,
        };
        
        let log_capture = LogCapture::new(log_config, None);
        let _ = log_capture.start_capture();
        
        // Initialize environment variable edit values
        let mut env_edit_values = HashMap::new();
        for (key, var) in &env_manager.variables {
            env_edit_values.insert(key.clone(), var.value.clone());
        }
        
        Self {
            shared_state,
            env_manager,
            log_capture,
            show_advanced: false,
            show_terminal: false,
            selected_tab: MainTab::Controls,
            env_edit_values,
            env_validation_errors: HashMap::new(),
            terminal_scroll_to_bottom: true,
            terminal_search_query: String::new(),
            terminal_auto_scroll: true,
            colors: AppColors::default(),
        }
    }
    
    fn setup_fonts(&self, ctx: &egui::Context) {
        // For now, we'll use the default fonts with custom sizing
        // In a real implementation, you would include Open Sans font files
        let fonts = egui::FontDefinitions::default();

        // We'll use default fonts for now
        ctx.set_fonts(fonts);
    }
    
    fn setup_style(&self, ctx: &egui::Context) {
        let mut style = (*ctx.style()).clone();
        
        // Dark theme colors
        style.visuals.dark_mode = true;
        style.visuals.override_text_color = Some(self.colors.text_primary);
        style.visuals.panel_fill = self.colors.surface;
        style.visuals.window_fill = self.colors.background;
        style.visuals.extreme_bg_color = self.colors.background;
        
        // Button styling
        style.visuals.widgets.inactive.bg_fill = self.colors.secondary;
        style.visuals.widgets.hovered.bg_fill = self.colors.primary;
        style.visuals.widgets.active.bg_fill = self.colors.accent;
        
        // Spacing
        style.spacing.item_spacing = egui::vec2(8.0, 6.0);
        style.spacing.button_padding = egui::vec2(12.0, 8.0);
        style.spacing.indent = 20.0;
        
        ctx.set_style(style);
    }
}

impl eframe::App for TradingWidgetApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Setup fonts and styling
        self.setup_style(ctx);
        
        // Process messages from bot
        self.process_bot_messages();
        
        // Main UI
        egui::CentralPanel::default().show(ctx, |ui| {
            self.render_main_ui(ui);
        });
        
        // Request repaint for real-time updates
        ctx.request_repaint_after(std::time::Duration::from_millis(100));
    }
}

impl TradingWidgetApp {
    fn process_bot_messages(&mut self) {
        // In a real implementation, this would process messages from the shared state
        // For now, we'll simulate some updates
    }
    
    fn render_main_ui(&mut self, ui: &mut egui::Ui) {
        // Top bar with title and main controls
        ui.horizontal(|ui| {
            ui.heading(RichText::new("PumpFun Sniper Trading Widget")
                .size(24.0)
                .color(self.colors.text_primary));
            
            ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                // Emergency stop button
                let emergency_active = self.shared_state.lock().unwrap().is_emergency_stop();
                let button_color = if emergency_active { self.colors.error } else { self.colors.warning };
                
                if ui.add(egui::Button::new(RichText::new("🚨 EMERGENCY STOP")
                    .color(Color32::WHITE))
                    .fill(button_color)
                    .min_size(egui::vec2(120.0, 30.0)))
                    .clicked() {
                    self.shared_state.lock().unwrap().trigger_emergency_stop();
                }
                
                ui.separator();
                
                // Terminal button
                if ui.add(egui::Button::new("Terminal")
                    .fill(if self.show_terminal { self.colors.primary } else { self.colors.secondary }))
                    .clicked() {
                    self.show_terminal = !self.show_terminal;
                }
                
                // Advanced button
                if ui.add(egui::Button::new("Advanced")
                    .fill(if self.show_advanced { self.colors.primary } else { self.colors.secondary }))
                    .clicked() {
                    self.show_advanced = !self.show_advanced;
                }
            });
        });
        
        ui.separator();
        
        // Main content area
        if self.show_terminal {
            self.render_terminal_panel(ui);
        } else {
            // Token aging pipeline visualization at the top
            self.render_token_pipeline_section(ui);
            
            ui.separator();
            
            // Active trade monitoring
            self.render_active_trade_section(ui);
            
            ui.separator();
            
            // Environment variable controls
            if self.show_advanced {
                self.render_advanced_controls(ui);
            } else {
                self.render_basic_controls(ui);
            }
        }
    }
    
    fn render_token_pipeline_section(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            ui.label(RichText::new("Token Aging Pipeline")
                .size(18.0)
                .color(self.colors.text_primary));
            
            let shared_state = self.shared_state.lock().unwrap();
            
            if shared_state.aging_tokens.is_empty() {
                ui.label(RichText::new("No tokens in aging pipeline")
                    .color(self.colors.text_secondary));
            } else {
                for (_mint, token) in &shared_state.aging_tokens {
                    self.render_token_progress_bar(ui, token);
                }
            }
        });
    }
    
    fn render_token_progress_bar(&self, ui: &mut egui::Ui, token: &TokenInfo) {
        ui.horizontal(|ui| {
            // Token info
            ui.label(RichText::new(&format!("{} ({})", token.name, token.symbol))
                .color(self.colors.text_primary));
            
            // Progress bar with gradient
            let progress = token.progress.clamp(0.0, 1.0) as f32;
            let bar_width = 200.0;
            let bar_height = 20.0;

            let (rect, _) = ui.allocate_exact_size(
                egui::vec2(bar_width, bar_height),
                egui::Sense::hover()
            );

            // Background
            ui.painter().rect_filled(rect, 4.0, self.colors.surface);

            // Progress fill with gradient effect
            if progress > 0.0 {
                let fill_width = bar_width * progress;
                let fill_rect = egui::Rect::from_min_size(
                    rect.min,
                    egui::vec2(fill_width, bar_height)
                );

                // Simple gradient approximation
                let color = if progress < 0.5 {
                    // Red to yellow
                    let t = progress * 2.0;
                    Color32::from_rgb(
                        244,
                        (67.0 + (193.0 - 67.0) * t) as u8,
                        (54.0 + (7.0 - 54.0) * t) as u8,
                    )
                } else {
                    // Yellow to green
                    let t = (progress - 0.5) * 2.0;
                    Color32::from_rgb(
                        (255.0 - (255.0 - 76.0) * t) as u8,
                        (193.0 - (193.0 - 175.0) * t) as u8,
                        (7.0 + (80.0 - 7.0) * t) as u8,
                    )
                };

                ui.painter().rect_filled(fill_rect, 4.0, color);
            }

            // Border
            ui.painter().rect_stroke(rect, 4.0, Stroke::new(1.0, self.colors.secondary), egui::epaint::StrokeKind::Outside);
            
            // Progress text
            ui.label(RichText::new(&format!("{:.1}%", progress * 100.0))
                .color(self.colors.text_secondary));
        });
    }
    
    fn render_active_trade_section(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            ui.label(RichText::new("Active Trade")
                .size(18.0)
                .color(self.colors.text_primary));
            
            let shared_state = self.shared_state.lock().unwrap();
            
            if let Some(trade) = &shared_state.active_trade {
                self.render_trade_progress(ui, trade);
            } else {
                ui.label(RichText::new("No active trades")
                    .color(self.colors.text_secondary));
            }
        });
    }
    
    fn render_trade_progress(&self, ui: &mut egui::Ui, trade: &ActiveTrade) {
        ui.horizontal(|ui| {
            ui.label(RichText::new(&format!("{} ({})", trade.token_name, trade.token_symbol))
                .color(self.colors.text_primary));
            
            // PnL indicator
            let pnl_color = if trade.pnl_percent >= 0.0 { self.colors.success } else { self.colors.error };
            ui.label(RichText::new(&format!("{:+.2}%", trade.pnl_percent))
                .color(pnl_color));
            
            // Status
            let status_text = match trade.status {
                TradeStatus::Buying => "🔄 Buying",
                TradeStatus::Holding => "📈 Holding",
                TradeStatus::Selling => "💰 Selling",
                TradeStatus::Completed => "✅ Completed",
                TradeStatus::Failed => "❌ Failed",
            };
            ui.label(RichText::new(status_text).color(self.colors.text_secondary));
        });
    }
    
    fn render_basic_controls(&mut self, ui: &mut egui::Ui) {
        // 3-column layout for basic controls
        ui.columns(3, |columns| {
            // Left column: Token Scoring Thresholds and Scoring Parameters
            columns[0].group(|ui| {
                self.render_env_group(ui, &EnvVarGroup::TokenScoringThresholds);
                ui.separator();
                self.render_env_group(ui, &EnvVarGroup::ScoringParameters);
            });
            
            // Center column: Token Scoring Weights (pie chart) and Bonding Curve Monitoring
            columns[1].group(|ui| {
                self.render_scoring_weights_pie_chart(ui);
                ui.separator();
                self.render_env_group(ui, &EnvVarGroup::BondingCurveMonitoring);
            });
            
            // Right column: Token Aging Pipeline and Token Filtering Thresholds
            columns[2].group(|ui| {
                self.render_env_group(ui, &EnvVarGroup::TokenAgingPipeline);
                ui.separator();
                self.render_env_group(ui, &EnvVarGroup::TokenFilteringThresholds);
            });
        });
    }
    
    fn render_env_group(&mut self, ui: &mut egui::Ui, group: &EnvVarGroup) {
        let group_name = match group {
            EnvVarGroup::TokenScoringThresholds => "Token Scoring Thresholds",
            EnvVarGroup::ScoringParameters => "Scoring Parameters",
            EnvVarGroup::TokenAgingPipeline => "Token Aging Pipeline",
            EnvVarGroup::TokenFilteringThresholds => "Token Filtering Thresholds",
            EnvVarGroup::BondingCurveMonitoring => "Bonding Curve Monitoring",
            _ => "Other Settings",
        };

        ui.label(RichText::new(group_name)
            .size(16.0)
            .color(self.colors.text_primary));

        // Collect variable data to avoid borrowing issues
        let variables: Vec<_> = self.env_manager.get_variables_by_group(group)
            .into_iter()
            .map(|var| (var.key.clone(), var.value.clone()))
            .collect();

        let mut updates = Vec::new();

        for (var_key, var_value) in variables {
            ui.horizontal(|ui| {
                ui.label(&var_key);

                let current_value = self.env_edit_values.get(&var_key)
                    .unwrap_or(&var_value)
                    .clone();

                let mut new_value = current_value;
                let response = ui.text_edit_singleline(&mut new_value);

                if response.changed() {
                    updates.push((var_key.clone(), new_value));
                }
            });

            // Show validation error if any
            if let Some(error) = self.env_validation_errors.get(&var_key) {
                ui.label(RichText::new(error).color(self.colors.error));
            }
        }

        // Process updates after the UI loop
        for (var_key, new_value) in updates {
            self.env_edit_values.insert(var_key.clone(), new_value.clone());
            // Validate and update
            if let Err(e) = self.env_manager.update_variable(&var_key, &new_value) {
                self.env_validation_errors.insert(var_key.clone(), e.to_string());
            } else {
                self.env_validation_errors.remove(&var_key);
            }
        }
    }
    
    fn render_scoring_weights_pie_chart(&mut self, ui: &mut egui::Ui) {
        ui.label(RichText::new("Token Scoring Weights")
            .size(16.0)
            .color(self.colors.text_primary));
        
        // Get the weight values
        let survival_weight = self.env_edit_values.get("PUMP_SURVIVAL_SCORE_WEIGHT")
            .and_then(|v| v.parse::<f64>().ok())
            .unwrap_or(0.15);
        let momentum_weight = self.env_edit_values.get("PUMP_MOMENTUM_SCORE_WEIGHT")
            .and_then(|v| v.parse::<f64>().ok())
            .unwrap_or(0.20);
        let graduation_weight = self.env_edit_values.get("PUMP_GRADUATION_SCORE_WEIGHT")
            .and_then(|v| v.parse::<f64>().ok())
            .unwrap_or(0.40);
        let risk_weight = self.env_edit_values.get("PUMP_RISK_SCORE_WEIGHT")
            .and_then(|v| v.parse::<f64>().ok())
            .unwrap_or(0.25);
        
        // Simple pie chart representation (placeholder)
        ui.label(format!("Survival: {:.1}%", survival_weight * 100.0));
        ui.label(format!("Momentum: {:.1}%", momentum_weight * 100.0));
        ui.label(format!("Graduation: {:.1}%", graduation_weight * 100.0));
        ui.label(format!("Risk: {:.1}%", risk_weight * 100.0));
        
        let total = survival_weight + momentum_weight + graduation_weight + risk_weight;
        if (total - 1.0).abs() > 0.01 {
            ui.label(RichText::new(format!("⚠️ Total: {:.1}% (should be 100%)", total * 100.0))
                .color(self.colors.warning));
        }
    }
    
    fn render_advanced_controls(&mut self, ui: &mut egui::Ui) {
        ui.label(RichText::new("Advanced Settings")
            .size(18.0)
            .color(self.colors.text_primary));
        
        // Show all environment variables in a scrollable list
        egui::ScrollArea::vertical().show(ui, |ui| {
            for (key, var) in &self.env_manager.variables {
                ui.group(|ui| {
                    ui.label(RichText::new(&var.key).color(self.colors.text_primary));
                    ui.label(RichText::new(&var.description).color(self.colors.text_secondary));
                    
                    let current_value = self.env_edit_values.get(key).unwrap_or(&var.value).clone();
                    let mut new_value = current_value;
                    
                    if var.is_sensitive {
                        // Show asterisks for sensitive values
                        ui.label("*".repeat(new_value.len().min(20)));
                        if ui.button("Paste").clicked() {
                            // In a real implementation, this would open a secure paste dialog
                        }
                    } else {
                        let response = ui.text_edit_singleline(&mut new_value);
                        if response.changed() {
                            self.env_edit_values.insert(key.clone(), new_value);
                        }
                    }
                });
            }
        });
    }
    
    fn render_terminal_panel(&mut self, ui: &mut egui::Ui) {
        ui.label(RichText::new("Terminal Logs")
            .size(18.0)
            .color(self.colors.text_primary));
        
        // Terminal controls
        ui.horizontal(|ui| {
            ui.text_edit_singleline(&mut self.terminal_search_query);
            if ui.button("Search").clicked() {
                let _ = self.log_capture.search(&self.terminal_search_query);
            }
            if ui.button("Clear").clicked() {
                self.log_capture.clear_logs();
            }
            if ui.button("Export").clicked() {
                // In a real implementation, this would open a file dialog
                let _ = self.log_capture.export_to_file("trading_logs.txt");
            }
        });
        
        // Log display
        egui::ScrollArea::vertical()
            .auto_shrink([false; 2])
            .show(ui, |ui| {
                let entries = self.log_capture.get_filtered_entries();
                for entry in entries {
                    ui.horizontal(|ui| {
                        ui.label(RichText::new(&entry.timestamp.format("%H:%M:%S").to_string())
                            .color(self.colors.text_secondary));
                        
                        let level_color = match entry.level.as_str() {
                            "ERROR" => self.colors.error,
                            "WARN" => self.colors.warning,
                            "INFO" => self.colors.text_primary,
                            _ => self.colors.text_secondary,
                        };
                        
                        ui.label(RichText::new(&entry.level).color(level_color));
                        ui.label(RichText::new(&entry.message).color(self.colors.text_primary));
                    });
                }
            });
    }
}
