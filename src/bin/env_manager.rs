//! Environment Variable Management
//! 
//! This module handles reading, parsing, validating, and writing .env files
//! with proper grouping and categorization of variables.

use std::collections::HashMap;
use std::fs;
use std::path::Path;
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};

/// Environment variable with metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvVar {
    pub key: String,
    pub value: String,
    pub description: String,
    pub var_type: EnvVarType,
    pub group: EnvVarGroup,
    pub is_sensitive: bool,
    pub validation: Option<EnvVarValidation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EnvVarType {
    String,
    Integer,
    Float,
    Boolean,
    Percentage, // 0.0 to 100.0
    Duration,   // in seconds, minutes, etc.
    Currency,   // SOL amounts
    Address,    // Wallet addresses, API keys
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum EnvVarGroup {
    // Basic controls (shown by default)
    TokenScoringThresholds,
    ScoringParameters,
    TokenAgingPipeline,
    TokenFilteringThresholds,
    TokenScoringWeights,
    BondingCurveMonitoring,
    
    // Advanced controls (hidden by default)
    WalletConfiguration,
    TradingSettings,
    SafetyLimits,
    TradeExecution,
    Monitoring,
    RateLimiting,
    ChainStackInfrastructure,
    TestingConfiguration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvVarValidation {
    pub min_value: Option<f64>,
    pub max_value: Option<f64>,
    pub allowed_values: Option<Vec<String>>,
    pub regex_pattern: Option<String>,
    pub required: bool,
}

/// Environment variable manager
pub struct EnvManager {
    pub variables: HashMap<String, EnvVar>,
    pub file_path: String,
}

impl EnvManager {
    pub fn new(file_path: &str) -> Result<Self> {
        let mut manager = Self {
            variables: HashMap::new(),
            file_path: file_path.to_string(),
        };
        
        manager.load_definitions()?;
        manager.load_values()?;
        
        Ok(manager)
    }
    
    /// Load variable definitions with metadata
    fn load_definitions(&mut self) -> Result<()> {
        // Token Scoring Thresholds
        self.add_var_definition("PUMP_MIN_SCORE_AGED_CANDIDATES", "0.3", 
            "Minimum score for aged graduation candidates", 
            EnvVarType::Float, EnvVarGroup::TokenScoringThresholds, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MIN_SCORE_FRESH_CANDIDATES", "0.35", 
            "Minimum score for fresh graduation candidates", 
            EnvVarType::Float, EnvVarGroup::TokenScoringThresholds, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MIN_TOTAL_SCORE", "0.7", 
            "Minimum total weighted score for trading", 
            EnvVarType::Float, EnvVarGroup::TokenScoringThresholds, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        // Scoring Parameters
        self.add_var_definition("PUMP_MIN_SURVIVAL_SCORE", "0.4", 
            "Minimum survival score (0.0-1.0)", 
            EnvVarType::Float, EnvVarGroup::ScoringParameters, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MIN_MOMENTUM_SCORE", "0.3", 
            "Minimum momentum score (0.0-1.0)", 
            EnvVarType::Float, EnvVarGroup::ScoringParameters, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MIN_GRADUATION_SCORE", "0.5", 
            "Minimum graduation score (0.0-1.0)", 
            EnvVarType::Float, EnvVarGroup::ScoringParameters, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MAX_RISK_SCORE", "0.8", 
            "Maximum acceptable risk score (0.0-1.0)", 
            EnvVarType::Float, EnvVarGroup::ScoringParameters, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        // Token Aging Pipeline
        self.add_var_definition("PUMP_AGING_PIPELINE_MAX_TRACKED_TOKENS", "23", 
            "Maximum tokens tracked simultaneously (RPC protection)", 
            EnvVarType::Integer, EnvVarGroup::TokenAgingPipeline, false,
            Some(EnvVarValidation { min_value: Some(1.0), max_value: Some(50.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_AGING_PIPELINE_CHECK_INTERVAL_SECONDS", "5", 
            "Check every N seconds", 
            EnvVarType::Duration, EnvVarGroup::TokenAgingPipeline, false,
            Some(EnvVarValidation { min_value: Some(1.0), max_value: Some(60.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_AGING_PIPELINE_MAX_AGE_SECONDS", "600", 
            "10 minutes maximum tracking", 
            EnvVarType::Duration, EnvVarGroup::TokenAgingPipeline, false,
            Some(EnvVarValidation { min_value: Some(60.0), max_value: Some(3600.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        // Token Filtering Thresholds
        self.add_var_definition("PUMP_MIN_MARKET_CAP_USD", "3000", 
            "Minimum market cap in USD", 
            EnvVarType::Currency, EnvVarGroup::TokenFilteringThresholds, false,
            Some(EnvVarValidation { min_value: Some(100.0), max_value: Some(1000000.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MAX_MARKET_CAP_USD", "************", 
            "Maximum market cap in USD", 
            EnvVarType::Currency, EnvVarGroup::TokenFilteringThresholds, false,
            Some(EnvVarValidation { min_value: Some(10000.0), max_value: None, 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MIN_LIQUIDITY_SOL", "40.0", 
            "Minimum liquidity in SOL", 
            EnvVarType::Currency, EnvVarGroup::TokenFilteringThresholds, false,
            Some(EnvVarValidation { min_value: Some(1.0), max_value: Some(10000.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        // Token Scoring Weights (must sum to 1.0)
        self.add_var_definition("PUMP_SURVIVAL_SCORE_WEIGHT", "0.15", 
            "Weight for survival score (age/stability)", 
            EnvVarType::Float, EnvVarGroup::TokenScoringWeights, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MOMENTUM_SCORE_WEIGHT", "0.20", 
            "Weight for momentum score (velocity/trading)", 
            EnvVarType::Float, EnvVarGroup::TokenScoringWeights, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_GRADUATION_SCORE_WEIGHT", "0.40", 
            "Weight for graduation score (bonding curve progress)", 
            EnvVarType::Float, EnvVarGroup::TokenScoringWeights, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_RISK_SCORE_WEIGHT", "0.25", 
            "Weight for risk score (liquidity/mcap risks)", 
            EnvVarType::Float, EnvVarGroup::TokenScoringWeights, false,
            Some(EnvVarValidation { min_value: Some(0.0), max_value: Some(1.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        // Bonding Curve Monitoring
        self.add_var_definition("PUMP_BONDING_CURVE_SCAN_INTERVAL", "120", 
            "Scan every 2 minutes (120 seconds)", 
            EnvVarType::Duration, EnvVarGroup::BondingCurveMonitoring, false,
            Some(EnvVarValidation { min_value: Some(30.0), max_value: Some(600.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        self.add_var_definition("PUMP_MAX_TRACKED_TOKENS", "23", 
            "Maximum tokens to track simultaneously", 
            EnvVarType::Integer, EnvVarGroup::BondingCurveMonitoring, false,
            Some(EnvVarValidation { min_value: Some(1.0), max_value: Some(50.0), 
                allowed_values: None, regex_pattern: None, required: true }));
        
        // Add more advanced variables here as needed
        // This is just a subset for the basic controls
        
        Ok(())
    }
    
    fn add_var_definition(&mut self, key: &str, default_value: &str, description: &str, 
                         var_type: EnvVarType, group: EnvVarGroup, is_sensitive: bool,
                         validation: Option<EnvVarValidation>) {
        self.variables.insert(key.to_string(), EnvVar {
            key: key.to_string(),
            value: default_value.to_string(),
            description: description.to_string(),
            var_type,
            group,
            is_sensitive,
            validation,
        });
    }
    
    /// Load actual values from .env file
    fn load_values(&mut self) -> Result<()> {
        if !Path::new(&self.file_path).exists() {
            return Ok(()); // File doesn't exist, use defaults
        }
        
        let content = fs::read_to_string(&self.file_path)?;
        
        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') {
                continue;
            }
            
            if let Some((key, value)) = line.split_once('=') {
                let key = key.trim();
                let value = value.trim();
                
                if let Some(var) = self.variables.get_mut(key) {
                    var.value = value.to_string();
                }
            }
        }
        
        Ok(())
    }
    
    /// Save all variables to .env file
    pub fn save_to_file(&self) -> Result<()> {
        let mut content = String::new();
        
        // Group variables by category
        let mut groups: HashMap<EnvVarGroup, Vec<&EnvVar>> = HashMap::new();
        for var in self.variables.values() {
            groups.entry(var.group.clone()).or_default().push(var);
        }
        
        // Write each group
        for (group, vars) in groups {
            content.push_str(&format!("\n# {}\n", group_name(&group)));
            for var in vars {
                if !var.description.is_empty() {
                    content.push_str(&format!("# {}\n", var.description));
                }
                content.push_str(&format!("{}={}\n", var.key, var.value));
            }
        }
        
        fs::write(&self.file_path, content)?;
        Ok(())
    }
    
    /// Get variables by group
    pub fn get_variables_by_group(&self, group: &EnvVarGroup) -> Vec<&EnvVar> {
        self.variables.values()
            .filter(|var| &var.group == group)
            .collect()
    }
    
    /// Update a variable value
    pub fn update_variable(&mut self, key: &str, value: &str) -> Result<()> {
        if let Some(var) = self.variables.get(key) {
            // Validate the value
            if let Some(validation) = &var.validation {
                self.validate_value(value, validation, &var.var_type)?;
            }
            // Now update the value
            if let Some(var) = self.variables.get_mut(key) {
                var.value = value.to_string();
            }
            Ok(())
        } else {
            Err(anyhow!("Variable {} not found", key))
        }
    }
    
    fn validate_value(&self, value: &str, validation: &EnvVarValidation, var_type: &EnvVarType) -> Result<()> {
        match var_type {
            EnvVarType::Float | EnvVarType::Percentage | EnvVarType::Currency => {
                let val: f64 = value.parse().map_err(|_| anyhow!("Invalid number format"))?;
                if let Some(min) = validation.min_value {
                    if val < min {
                        return Err(anyhow!("Value {} is below minimum {}", val, min));
                    }
                }
                if let Some(max) = validation.max_value {
                    if val > max {
                        return Err(anyhow!("Value {} is above maximum {}", val, max));
                    }
                }
            }
            EnvVarType::Integer | EnvVarType::Duration => {
                let val: i64 = value.parse().map_err(|_| anyhow!("Invalid integer format"))?;
                if let Some(min) = validation.min_value {
                    if (val as f64) < min {
                        return Err(anyhow!("Value {} is below minimum {}", val, min));
                    }
                }
                if let Some(max) = validation.max_value {
                    if (val as f64) > max {
                        return Err(anyhow!("Value {} is above maximum {}", val, max));
                    }
                }
            }
            EnvVarType::Boolean => {
                value.parse::<bool>().map_err(|_| anyhow!("Invalid boolean format"))?;
            }
            _ => {} // String and Address types don't need numeric validation
        }
        
        Ok(())
    }
}

fn group_name(group: &EnvVarGroup) -> &'static str {
    match group {
        EnvVarGroup::TokenScoringThresholds => "Token Scoring Thresholds",
        EnvVarGroup::ScoringParameters => "Scoring Parameters",
        EnvVarGroup::TokenAgingPipeline => "Token Aging Pipeline Configuration",
        EnvVarGroup::TokenFilteringThresholds => "Token Filtering Thresholds",
        EnvVarGroup::TokenScoringWeights => "Token Scoring Weights",
        EnvVarGroup::BondingCurveMonitoring => "Bonding Curve Monitoring",
        EnvVarGroup::WalletConfiguration => "Wallet Configuration",
        EnvVarGroup::TradingSettings => "Trading Settings",
        EnvVarGroup::SafetyLimits => "Safety Limits",
        EnvVarGroup::TradeExecution => "Trade Execution",
        EnvVarGroup::Monitoring => "Monitoring",
        EnvVarGroup::RateLimiting => "Rate Limiting",
        EnvVarGroup::ChainStackInfrastructure => "ChainStack Infrastructure",
        EnvVarGroup::TestingConfiguration => "Testing Configuration",
    }
}
