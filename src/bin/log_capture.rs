//! Log Capture System
//! 
//! This module captures terminal output in real-time without blocking trading operations.
//! It provides search, scroll, and export capabilities for the GUI.

use std::collections::VecDeque;
use std::sync::{Arc, Mutex};
use std::sync::mpsc::Sender;
use std::thread;
use std::time::Duration;
use chrono::{DateTime, Utc};
use regex::Regex;
use anyhow::Result;

use super::shared_state::{LogEntry, BotMessage};

/// Log capture configuration
#[derive(Debug, Clone)]
pub struct LogCaptureConfig {
    pub max_entries: usize,
    pub capture_enabled: bool,
    pub auto_scroll: bool,
    pub filter_level: LogLevel,
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
}

impl LogLevel {
    pub fn from_str(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "trace" => LogLevel::Trace,
            "debug" => LogLevel::Debug,
            "info" => LogLevel::Info,
            "warn" | "warning" => LogLevel::Warn,
            "error" => LogLevel::Error,
            _ => LogLevel::Info,
        }
    }
    
    pub fn to_string(&self) -> &'static str {
        match self {
            LogLevel::Trace => "TRACE",
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warn => "WARN",
            LogLevel::Error => "ERROR",
        }
    }
}

/// Log capture system
pub struct LogCapture {
    entries: Arc<Mutex<VecDeque<LogEntry>>>,
    config: LogCaptureConfig,
    sender: Option<Sender<BotMessage>>,
    search_results: Vec<usize>, // Indices of matching entries
    current_search: Option<String>,
}

impl LogCapture {
    pub fn new(config: LogCaptureConfig, sender: Option<Sender<BotMessage>>) -> Self {
        Self {
            entries: Arc::new(Mutex::new(VecDeque::with_capacity(config.max_entries))),
            config,
            sender,
            search_results: Vec::new(),
            current_search: None,
        }
    }
    
    /// Start capturing logs from the bot
    pub fn start_capture(&self) -> Result<()> {
        if !self.config.capture_enabled {
            return Ok(());
        }
        
        let entries = Arc::clone(&self.entries);
        let max_entries = self.config.max_entries;
        let sender = self.sender.clone();
        
        // Spawn a thread to simulate log capture
        // In a real implementation, this would read from the actual bot's log output
        thread::spawn(move || {
            let mut counter = 0;
            loop {
                // Simulate receiving log entries
                thread::sleep(Duration::from_millis(500));
                
                let entry = LogEntry {
                    timestamp: Utc::now(),
                    level: if counter % 10 == 0 { "ERROR".to_string() } 
                           else if counter % 5 == 0 { "WARN".to_string() }
                           else { "INFO".to_string() },
                    message: format!("Simulated log entry #{} - Trading bot activity", counter),
                    module: Some("trading_bot".to_string()),
                };
                
                // Add to internal storage
                {
                    let mut entries_guard = entries.lock().unwrap();
                    entries_guard.push_back(entry.clone());
                    
                    // Maintain max entries
                    while entries_guard.len() > max_entries {
                        entries_guard.pop_front();
                    }
                }
                
                // Send to GUI if sender is available
                if let Some(ref sender) = sender {
                    let _ = sender.send(BotMessage::LogEntry(entry));
                }
                
                counter += 1;
            }
        });
        
        Ok(())
    }
    
    /// Add a log entry manually
    pub fn add_entry(&self, entry: LogEntry) {
        let mut entries = self.entries.lock().unwrap();
        entries.push_back(entry.clone());
        
        // Maintain max entries
        while entries.len() > self.config.max_entries {
            entries.pop_front();
        }
        
        // Send to GUI if sender is available
        if let Some(ref sender) = self.sender {
            let _ = sender.send(BotMessage::LogEntry(entry));
        }
    }
    
    /// Get all log entries
    pub fn get_entries(&self) -> Vec<LogEntry> {
        let entries = self.entries.lock().unwrap();
        entries.iter().cloned().collect()
    }
    
    /// Get filtered log entries
    pub fn get_filtered_entries(&self) -> Vec<LogEntry> {
        let entries = self.entries.lock().unwrap();
        entries.iter()
            .filter(|entry| {
                let entry_level = LogLevel::from_str(&entry.level);
                entry_level >= self.config.filter_level
            })
            .cloned()
            .collect()
    }
    
    /// Search log entries
    pub fn search(&mut self, query: &str) -> Result<Vec<usize>> {
        if query.is_empty() {
            self.search_results.clear();
            self.current_search = None;
            return Ok(Vec::new());
        }
        
        let regex = Regex::new(&format!("(?i){}", regex::escape(query)))?;
        let entries = self.entries.lock().unwrap();
        
        self.search_results = entries.iter()
            .enumerate()
            .filter(|(_, entry)| {
                regex.is_match(&entry.message) || 
                entry.module.as_ref().map_or(false, |m| regex.is_match(m))
            })
            .map(|(i, _)| i)
            .collect();
        
        self.current_search = Some(query.to_string());
        Ok(self.search_results.clone())
    }
    
    /// Get search results
    pub fn get_search_results(&self) -> &[usize] {
        &self.search_results
    }
    
    /// Clear search
    pub fn clear_search(&mut self) {
        self.search_results.clear();
        self.current_search = None;
    }
    
    /// Export logs to file
    pub fn export_to_file(&self, file_path: &str) -> Result<()> {
        let entries = self.entries.lock().unwrap();
        let mut content = String::new();
        
        content.push_str("# PumpFun Trading Bot Log Export\n");
        content.push_str(&format!("# Exported at: {}\n", Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
        content.push_str(&format!("# Total entries: {}\n\n", entries.len()));
        
        for entry in entries.iter() {
            content.push_str(&format!(
                "[{}] [{}] {}{}\n",
                entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f"),
                entry.level,
                entry.module.as_ref().map_or(String::new(), |m| format!("[{}] ", m)),
                entry.message
            ));
        }
        
        std::fs::write(file_path, content)?;
        Ok(())
    }
    
    /// Get log statistics
    pub fn get_statistics(&self) -> LogStatistics {
        let entries = self.entries.lock().unwrap();
        let mut stats = LogStatistics::default();
        
        stats.total_entries = entries.len();
        
        for entry in entries.iter() {
            match entry.level.as_str() {
                "ERROR" => stats.error_count += 1,
                "WARN" => stats.warning_count += 1,
                "INFO" => stats.info_count += 1,
                "DEBUG" => stats.debug_count += 1,
                "TRACE" => stats.trace_count += 1,
                _ => {}
            }
        }
        
        if let (Some(first), Some(last)) = (entries.front(), entries.back()) {
            stats.time_range = Some((first.timestamp, last.timestamp));
        }
        
        stats
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: LogCaptureConfig) {
        self.config = config;
    }
    
    /// Clear all log entries
    pub fn clear_logs(&mut self) {
        let mut entries = self.entries.lock().unwrap();
        entries.clear();
        self.search_results.clear();
    }
}

/// Log statistics for display
#[derive(Debug, Default)]
pub struct LogStatistics {
    pub total_entries: usize,
    pub error_count: usize,
    pub warning_count: usize,
    pub info_count: usize,
    pub debug_count: usize,
    pub trace_count: usize,
    pub time_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
}

impl LogStatistics {
    pub fn get_level_percentage(&self, level: &LogLevel) -> f32 {
        if self.total_entries == 0 {
            return 0.0;
        }
        
        let count = match level {
            LogLevel::Error => self.error_count,
            LogLevel::Warn => self.warning_count,
            LogLevel::Info => self.info_count,
            LogLevel::Debug => self.debug_count,
            LogLevel::Trace => self.trace_count,
        };
        
        (count as f32 / self.total_entries as f32) * 100.0
    }
}
