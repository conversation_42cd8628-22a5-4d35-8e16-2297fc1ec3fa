//! PumpFun Sniper Trading Widget
//! 
//! A native GUI application for controlling and monitoring the PumpFun trading bot.
//! This application provides:
//! - Environment variable configuration
//! - Real-time token aging pipeline visualization
//! - Active trade monitoring
//! - Terminal log viewing and export
//! - Dark themed professional interface

use eframe::egui;
use std::sync::{Arc, Mutex};
use anyhow::Result;

mod gui;
mod shared_state;
mod env_manager;
mod log_capture;
mod integration_test;

use gui::TradingWidgetApp;
use shared_state::SharedState;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();

    // Load environment variables
    dotenv::dotenv().ok();

    println!("🎯 Starting PumpFun Sniper Trading Widget...");

    // Check for test mode
    let args: Vec<String> = std::env::args().collect();
    if args.len() > 1 && args[1] == "--test" {
        println!("🧪 Running in test mode...");

        // Run integration tests
        if let Err(e) = integration_test::test_gui_components() {
            eprintln!("❌ GUI components test failed: {}", e);
            return Ok(());
        }

        if let Err(e) = integration_test::run_integration_test().await {
            eprintln!("❌ Integration test failed: {}", e);
            return Ok(());
        }

        if let Err(e) = integration_test::test_gui_performance().await {
            eprintln!("❌ Performance test failed: {}", e);
            return Ok(());
        }

        println!("🎉 All tests passed! GUI is ready for production use.");
        return Ok(());
    }

    // Create shared state for communication between GUI and bot
    let shared_state = Arc::new(Mutex::new(SharedState::new()));

    // Configure the native GUI options
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([1400.0, 900.0])
            .with_min_inner_size([1200.0, 800.0])
            .with_title("PumpFun Sniper Trading Widget")
            .with_icon(load_icon()),
        ..Default::default()
    };

    // Create the application
    let app = TradingWidgetApp::new(shared_state);

    // Run the GUI
    eframe::run_native(
        "PumpFun Sniper Trading Widget",
        options,
        Box::new(|_cc| Ok(Box::new(app))),
    ).map_err(|e| anyhow::anyhow!("Failed to run GUI: {}", e))
}

fn load_icon() -> egui::IconData {
    // Create a simple icon for the application
    // In a real implementation, you'd load this from a file
    egui::IconData {
        rgba: vec![255; 32 * 32 * 4], // Simple white 32x32 icon
        width: 32,
        height: 32,
    }
}
