//! Shared State Management
//! 
//! This module provides thread-safe communication between the GUI and the trading bot core logic.
//! It uses channels and atomic operations to ensure non-blocking operation.

use std::collections::HashMap;
use std::sync::atomic::{AtomicBool, AtomicU32, Ordering};
use std::sync::mpsc::{Receiver, Sender};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Token information for the aging pipeline visualization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenInfo {
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub score: f64,
    pub score_threshold: f64,
    pub progress: f64, // 0.0 to 1.0
    pub age_seconds: u64,
    pub bonding_curve_progress: f64,
    pub last_updated: DateTime<Utc>,
}

/// Active trade information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActiveTrade {
    pub token_mint: String,
    pub token_name: String,
    pub token_symbol: String,
    pub entry_price: f64,
    pub current_price: f64,
    pub position_size: f64,
    pub pnl_percent: f64,
    pub trade_start_time: DateTime<Utc>,
    pub progress: f64, // 0.0 to 1.0 based on time or other factors
    pub status: TradeStatus,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TradeStatus {
    Buying,
    Holding,
    Selling,
    Completed,
    Failed,
}

/// Log entry for terminal display
#[derive(Debug, Clone)]
pub struct LogEntry {
    pub timestamp: DateTime<Utc>,
    pub level: String,
    pub message: String,
    pub module: Option<String>,
}

/// Messages that can be sent from the bot to the GUI
#[derive(Debug, Clone)]
pub enum BotMessage {
    TokenAdded(TokenInfo),
    TokenUpdated(TokenInfo),
    TokenRemoved(String), // mint address
    TradeStarted(ActiveTrade),
    TradeUpdated(ActiveTrade),
    TradeCompleted(ActiveTrade),
    LogEntry(LogEntry),
    StatusUpdate(String),
}

/// Messages that can be sent from the GUI to the bot
#[derive(Debug, Clone)]
pub enum GuiMessage {
    UpdateEnvironmentVariable(String, String), // key, value
    ReloadConfiguration,
    PauseTrading,
    ResumeTrading,
    EmergencyStop,
    ExportLogs(String), // file path
}

/// Shared state between GUI and bot
pub struct SharedState {
    // Atomic flags for simple state
    pub trading_active: AtomicBool,
    pub emergency_stop: AtomicBool,
    pub total_trades: AtomicU32,
    
    // Token aging pipeline
    pub aging_tokens: HashMap<String, TokenInfo>, // mint -> TokenInfo
    
    // Active trade
    pub active_trade: Option<ActiveTrade>,
    
    // Terminal logs (limited to last N entries for performance)
    pub log_entries: Vec<LogEntry>,
    pub max_log_entries: usize,
    
    // Communication channels
    pub bot_to_gui_sender: Option<Sender<BotMessage>>,
    pub bot_to_gui_receiver: Option<Receiver<BotMessage>>,
    pub gui_to_bot_sender: Option<Sender<GuiMessage>>,
    pub gui_to_bot_receiver: Option<Receiver<GuiMessage>>,
    
    // Environment variables cache
    pub env_vars: HashMap<String, String>,
    pub env_vars_dirty: AtomicBool, // Flag to indicate env vars need saving
}

impl SharedState {
    pub fn new() -> Self {
        let (bot_to_gui_tx, bot_to_gui_rx) = std::sync::mpsc::channel();
        let (gui_to_bot_tx, gui_to_bot_rx) = std::sync::mpsc::channel();
        
        Self {
            trading_active: AtomicBool::new(false),
            emergency_stop: AtomicBool::new(false),
            total_trades: AtomicU32::new(0),
            aging_tokens: HashMap::new(),
            active_trade: None,
            log_entries: Vec::new(),
            max_log_entries: 1000, // Keep last 1000 log entries
            bot_to_gui_sender: Some(bot_to_gui_tx),
            bot_to_gui_receiver: Some(bot_to_gui_rx),
            gui_to_bot_sender: Some(gui_to_bot_tx),
            gui_to_bot_receiver: Some(gui_to_bot_rx),
            env_vars: HashMap::new(),
            env_vars_dirty: AtomicBool::new(false),
        }
    }
    
    /// Add or update a token in the aging pipeline
    pub fn update_token(&mut self, token: TokenInfo) {
        self.aging_tokens.insert(token.mint.clone(), token);
    }
    
    /// Remove a token from the aging pipeline
    pub fn remove_token(&mut self, mint: &str) {
        self.aging_tokens.remove(mint);
    }
    
    /// Add a log entry, maintaining the maximum count
    pub fn add_log_entry(&mut self, entry: LogEntry) {
        self.log_entries.push(entry);
        
        // Keep only the last max_log_entries
        if self.log_entries.len() > self.max_log_entries {
            self.log_entries.drain(0..self.log_entries.len() - self.max_log_entries);
        }
    }
    
    /// Get trading status
    pub fn is_trading_active(&self) -> bool {
        self.trading_active.load(Ordering::Relaxed)
    }
    
    /// Set trading status
    pub fn set_trading_active(&self, active: bool) {
        self.trading_active.store(active, Ordering::Relaxed);
    }
    
    /// Check if emergency stop is active
    pub fn is_emergency_stop(&self) -> bool {
        self.emergency_stop.load(Ordering::Relaxed)
    }
    
    /// Trigger emergency stop
    pub fn trigger_emergency_stop(&self) {
        self.emergency_stop.store(true, Ordering::Relaxed);
    }
    
    /// Get total trades count
    pub fn get_total_trades(&self) -> u32 {
        self.total_trades.load(Ordering::Relaxed)
    }
    
    /// Increment total trades count
    pub fn increment_trades(&self) {
        self.total_trades.fetch_add(1, Ordering::Relaxed);
    }
}

impl Default for SharedState {
    fn default() -> Self {
        Self::new()
    }
}
